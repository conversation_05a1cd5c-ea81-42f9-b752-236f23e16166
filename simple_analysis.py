#!/usr/bin/env python3
"""
Simple but robust PE analysis for suspicious strings and patterns
Works directly with system environment - no virtual environments needed
"""

import re
import sys
import os
import subprocess
import platform

def detect_system_tools():
    """Detect available system analysis tools"""
    tools = {}

    # Check for Windows tools
    if platform.system() == "Windows":
        # Try to find dumpbin
        try:
            result = subprocess.run(['where', 'dumpbin'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                tools['dumpbin'] = result.stdout.strip().split('\n')[0]
        except:
            pass

        # Try to find objdump (from MinGW/MSYS2)
        try:
            result = subprocess.run(['where', 'objdump'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                tools['objdump'] = result.stdout.strip().split('\n')[0]
        except:
            pass

        # Try to find strings utility
        try:
            result = subprocess.run(['where', 'strings'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                tools['strings'] = result.stdout.strip().split('\n')[0]
        except:
            pass

    return tools

def extract_imports_with_system_tools(filepath):
    """Extract imports using system tools if available"""
    tools = detect_system_tools()
    imports_info = []

    # Try dumpbin first (Windows native)
    if 'dumpbin' in tools:
        try:
            result = subprocess.run([tools['dumpbin'], '/imports', filepath],
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                imports_info.append(("dumpbin", result.stdout))
        except Exception as e:
            print(f"[-] Error running dumpbin: {e}")

    # Try objdump if available
    if 'objdump' in tools:
        try:
            result = subprocess.run([tools['objdump'], '-p', filepath],
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                imports_info.append(("objdump", result.stdout))
        except Exception as e:
            print(f"[-] Error running objdump: {e}")

    return imports_info

def extract_strings_with_system_tools(filepath):
    """Extract strings using system tools if available"""
    tools = detect_system_tools()

    # Try system strings utility first
    if 'strings' in tools:
        try:
            result = subprocess.run([tools['strings'], filepath],
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                return result.stdout.split('\n')
        except Exception as e:
            print(f"[-] Error running strings utility: {e}")

    # Fallback to Python implementation
    return None

def extract_all_strings(filepath, min_len=4):
    """Extract all readable strings from binary"""
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading file: {e}")
        return []
    
    strings = []
    
    # ASCII strings
    ascii_pattern = rb'[\x20-\x7E]{' + str(min_len).encode() + rb',}'
    ascii_matches = re.findall(ascii_pattern, data)
    strings.extend([s.decode('ascii', errors='ignore') for s in ascii_matches])
    
    # Unicode strings (UTF-16LE)
    unicode_pattern = rb'(?:[\x20-\x7E]\x00){' + str(min_len).encode() + rb',}'
    unicode_matches = re.findall(unicode_pattern, data)
    for match in unicode_matches:
        try:
            decoded = match.decode('utf-16le', errors='ignore')
            if len(decoded) >= min_len:
                strings.append(decoded)
        except:
            pass
    
    return list(set(strings))

def analyze_malware_indicators(strings):
    """Analyze strings for malware indicators"""
    
    indicators = {
        'process_injection': {
            'patterns': [
                r'(?i)(virtualalloc|virtualprotect|writeprocessmemory|readprocessmemory)',
                r'(?i)(createremotethread|ntcreatethreadex|rtlcreateuserthread)',
                r'(?i)(queueuserapc|setthreadcontext|resumethread|suspendthread)',
                r'(?i)(inject|shellcode|payload|allocate)'
            ],
            'matches': []
        },
        'poolparty_technique': {
            'patterns': [
                r'(?i)(hijack.*handle|completion.*port|worker.*factory)',
                r'(?i)(tp_direct|iocompletion|zwsetiocompletion|ntsetiocompletion)',
                r'(?i)(poolparty|thread.*pool|i/o.*completion)'
            ],
            'matches': []
        },
        'module_stomping': {
            'patterns': [
                r'(?i)(module.*stomp|dll.*hollow|section.*map)',
                r'(?i)(ntmapviewofsection|zwmapviewofsection)',
                r'(?i)(xpsservices|dllcanunloadnow|loadlibrary)'
            ],
            'matches': []
        },
        'evasion_techniques': {
            'patterns': [
                r'(?i)(antidebug|antivm|sandbox|detection)',
                r'(?i)(isdebuggerpresent|checkremotedebuggerpresent)',
                r'(?i)(sleep|delay|timing|cpuid|rdtsc)',
                r'(?i)(debug|manual.*mapping|peb.*walk)'
            ],
            'matches': []
        },
        'syscalls_direct': {
            'patterns': [
                r'(?i)(nt[a-z]+|zw[a-z]+)',
                r'(?i)(sw3_|syscall|syswhispers)',
                r'(?i)(direct.*syscall|native.*api)'
            ],
            'matches': []
        },
        'target_processes': {
            'patterns': [
                r'(?i)(mstsc\.exe|explorer\.exe|winlogon\.exe)',
                r'(?i)(svchost\.exe|lsass\.exe|csrss\.exe)',
                r'(?i)(enumprocess|process32|openprocess)'
            ],
            'matches': []
        },
        'crypto_obfuscation': {
            'patterns': [
                r'(?i)(encrypt|decrypt|xor|cipher|key)',
                r'(?i)(skcrypt|obfuscat|encode|decode)',
                r'(?i)(base64|hex|crypt)'
            ],
            'matches': []
        },
        'debug_artifacts': {
            'patterns': [
                r'(?i)(\[info\]|\[debug\]|\[error\]|\[found\])',
                r'(?i)(allocshim|remotefunc|res \()',
                r'(?i)(hijacked.*handle|crafted.*structure)'
            ],
            'matches': []
        }
    }
    
    # Analyze each string against all patterns
    for string in strings:
        for category, info in indicators.items():
            for pattern in info['patterns']:
                if re.search(pattern, string):
                    info['matches'].append(string)
                    break  # Only count each string once per category
    
    return indicators

def find_obfuscated_apis(strings):
    """Find potential obfuscated API names"""
    obfuscated_apis = []
    
    # Look for random-looking function names that might be obfuscated APIs
    for string in strings:
        # Pattern: random letters/numbers, typically 15-25 chars
        if re.match(r'^[a-zA-Z][a-zA-Z0-9]{14,24}$', string):
            # Check if it appears in context that suggests it's an API
            if any(keyword in string.lower() for keyword in ['res', 'call', 'func']):
                obfuscated_apis.append(string)
    
    return obfuscated_apis

def extract_target_info(strings):
    """Extract specific target information"""
    targets = {
        'processes': [],
        'dlls': [],
        'functions': [],
        'registry_keys': [],
        'file_paths': []
    }
    
    for string in strings:
        # Process names
        if re.search(r'\.exe$', string, re.IGNORECASE):
            targets['processes'].append(string)
        
        # DLL names
        if re.search(r'\.dll$', string, re.IGNORECASE):
            targets['dlls'].append(string)
        
        # Function names (Windows API pattern)
        if re.match(r'^[A-Z][a-zA-Z0-9]{3,30}$', string):
            targets['functions'].append(string)
        
        # Registry keys
        if re.search(r'(HKEY_|SOFTWARE\\|SYSTEM\\)', string, re.IGNORECASE):
            targets['registry_keys'].append(string)
        
        # File paths
        if re.search(r'[A-Z]:\\|\\\\', string):
            targets['file_paths'].append(string)
    
    return targets

def print_analysis_results(indicators, obfuscated_apis, targets, total_strings):
    """Print formatted analysis results"""
    
    print("="*80)
    print("ROBUST MALWARE ANALYSIS RESULTS")
    print("="*80)
    
    print(f"\nTOTAL STRINGS EXTRACTED: {total_strings}")
    
    # Malware indicators
    print(f"\nMALWARE INDICATORS:")
    print("-" * 40)
    
    total_indicators = 0
    for category, info in indicators.items():
        matches = info['matches']
        if matches:
            total_indicators += len(matches)
            print(f"\n{category.upper().replace('_', ' ')} ({len(matches)} matches):")
            for match in matches[:10]:  # Limit display
                print(f"  • {match[:70]}{'...' if len(match) > 70 else ''}")
            if len(matches) > 10:
                print(f"  ... and {len(matches) - 10} more")
    
    print(f"\nTOTAL SUSPICIOUS INDICATORS: {total_indicators}")
    
    # Obfuscated APIs
    if obfuscated_apis:
        print(f"\nOBFUSCATED API NAMES ({len(obfuscated_apis)} found):")
        print("-" * 40)
        for api in obfuscated_apis[:15]:
            print(f"  • {api}")
        if len(obfuscated_apis) > 15:
            print(f"  ... and {len(obfuscated_apis) - 15} more")
    
    # Target information
    print(f"\nTARGET INFORMATION:")
    print("-" * 40)
    
    for target_type, items in targets.items():
        if items:
            unique_items = list(set(items))
            print(f"\n{target_type.upper().replace('_', ' ')} ({len(unique_items)} unique):")
            for item in unique_items[:10]:
                print(f"  • {item}")
            if len(unique_items) > 10:
                print(f"  ... and {len(unique_items) - 10} more")

def main():
    if len(sys.argv) != 2:
        print("Usage: python simple_analysis.py <pe_file>")
        sys.exit(1)

    filepath = sys.argv[1]
    if not os.path.exists(filepath):
        print(f"Error: File '{filepath}' not found")
        sys.exit(1)

    print(f"Analyzing: {filepath}")
    print(f"File size: {os.path.getsize(filepath):,} bytes")
    print(f"Platform: {platform.system()}")

    # Detect available system tools
    print("\nDetecting system analysis tools...")
    tools = detect_system_tools()
    if tools:
        print("Available tools:")
        for tool, path in tools.items():
            print(f"  - {tool}: {path}")
    else:
        print("  - No system tools detected, using Python implementation")

    # Extract strings (try system tools first)
    print("\nExtracting strings...")
    system_strings = extract_strings_with_system_tools(filepath)
    if system_strings:
        print("  - Using system strings utility")
        strings = [s for s in system_strings if len(s) >= 4]
    else:
        print("  - Using Python string extraction")
        strings = extract_all_strings(filepath, min_len=4)

    # Extract imports using system tools
    print("\nExtracting imports...")
    imports_info = extract_imports_with_system_tools(filepath)
    if imports_info:
        print("  - System tool import analysis available")
        for tool, output in imports_info:
            print(f"    * {tool} output captured")

    # Analyze for malware indicators
    print("Analyzing malware indicators...")
    indicators = analyze_malware_indicators(strings)

    # Find obfuscated APIs
    print("Searching for obfuscated APIs...")
    obfuscated_apis = find_obfuscated_apis(strings)

    # Extract target information
    print("Extracting target information...")
    targets = extract_target_info(strings)

    # Print results
    print_analysis_results(indicators, obfuscated_apis, targets, len(strings))

    # Print system tool results if available
    if imports_info:
        print("\n" + "="*80)
        print("SYSTEM TOOL ANALYSIS RESULTS")
        print("="*80)
        for tool, output in imports_info:
            print(f"\n{tool.upper()} OUTPUT:")
            print("-" * 40)
            # Show first 50 lines of output
            lines = output.split('\n')[:50]
            for line in lines:
                if line.strip():
                    print(f"  {line}")
            if len(output.split('\n')) > 50:
                print(f"  ... ({len(output.split('\n')) - 50} more lines)")

if __name__ == "__main__":
    main()
