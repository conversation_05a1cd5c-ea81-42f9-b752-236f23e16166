#!/usr/bin/env python3
"""
Robust PE Analysis Tool for Suspicious Strings and Imports
Analyzes PE files for malicious indicators including IAT, strings, and suspicious patterns
"""

import sys
import re
import struct
import os
from typing import List, Dict, Set, Tuple

class PEAnalyzer:
    def __init__(self, filepath: str):
        self.filepath = filepath
        self.data = None
        self.pe_header_offset = 0
        self.sections = []
        self.imports = []
        self.suspicious_strings = []
        self.suspicious_imports = []
        
    def load_file(self) -> bool:
        """Load the PE file into memory"""
        try:
            with open(self.filepath, 'rb') as f:
                self.data = f.read()
            return True
        except Exception as e:
            print(f"[-] Error loading file: {e}")
            return False
    
    def parse_pe_header(self) -> bool:
        """Parse PE header to get basic information"""
        try:
            # Check DOS header
            if self.data[:2] != b'MZ':
                print("[-] Not a valid PE file (missing MZ signature)")
                return False
            
            # Get PE header offset
            self.pe_header_offset = struct.unpack('<I', self.data[0x3C:0x40])[0]
            
            # Check PE signature
            if self.data[self.pe_header_offset:self.pe_header_offset+4] != b'PE\x00\x00':
                print("[-] Not a valid PE file (missing PE signature)")
                return False
            
            print(f"[+] Valid PE file detected")
            print(f"[+] PE header offset: 0x{self.pe_header_offset:x}")
            return True
            
        except Exception as e:
            print(f"[-] Error parsing PE header: {e}")
            return False
    
    def extract_strings(self, min_length: int = 4) -> List[str]:
        """Extract ASCII and Unicode strings from the binary"""
        strings = []
        
        # ASCII strings
        ascii_pattern = rb'[\x20-\x7E]{' + str(min_length).encode() + rb',}'
        ascii_matches = re.findall(ascii_pattern, self.data)
        strings.extend([s.decode('ascii', errors='ignore') for s in ascii_matches])
        
        # Unicode strings (UTF-16LE)
        unicode_pattern = rb'(?:[\x20-\x7E]\x00){' + str(min_length).encode() + rb',}'
        unicode_matches = re.findall(unicode_pattern, self.data)
        for match in unicode_matches:
            try:
                decoded = match.decode('utf-16le', errors='ignore')
                if len(decoded) >= min_length:
                    strings.append(decoded)
            except:
                pass
        
        return list(set(strings))  # Remove duplicates
    
    def analyze_suspicious_strings(self, strings: List[str]) -> List[Dict]:
        """Analyze strings for suspicious patterns"""
        suspicious_patterns = {
            'process_injection': [
                r'(?i)(inject|shellcode|payload|allocate|virtualalloc|writeprocessmemory)',
                r'(?i)(createremotethread|ntcreatethreadex|rtlcreateuserthread)',
                r'(?i)(virtualprotect|ntprotectvirtualmemory|flushinstruction)',
                r'(?i)(process32|enumprocess|openprocess|getprocaddress)'
            ],
            'evasion': [
                r'(?i)(antidebug|antivm|sandbox|sleep|delay|check)',
                r'(?i)(isdebuggerpresent|checkremotedebuggerpresent)',
                r'(?i)(ntqueryinformationprocess|zwqueryinformationprocess)',
                r'(?i)(cpuid|rdtsc|timing|detection)'
            ],
            'persistence': [
                r'(?i)(registry|regkey|hkey_|software\\microsoft)',
                r'(?i)(startup|autorun|service|schtasks|task)',
                r'(?i)(winlogon|userinit|shell|explorer)'
            ],
            'network': [
                r'(?i)(http|https|ftp|tcp|udp|socket|connect)',
                r'(?i)(download|upload|c2|command|control)',
                r'(?i)(wininet|winhttp|urlmon|wsock)'
            ],
            'crypto': [
                r'(?i)(encrypt|decrypt|cipher|key|xor|aes|rc4)',
                r'(?i)(hash|md5|sha|crypt|encode|decode)',
                r'(?i)(base64|hex|obfuscat)'
            ],
            'poolparty': [
                r'(?i)(poolparty|tp_direct|io.*completion|worker.*factory)',
                r'(?i)(hijack.*handle|completion.*port|thread.*pool)',
                r'(?i)(ntsetiocompletion|zwsetiocompletion)'
            ],
            'module_stomping': [
                r'(?i)(module.*stomp|dll.*hollow|section.*map)',
                r'(?i)(ntmapviewofsection|zwmapviewofsection)',
                r'(?i)(imagebase|dllmain|loadlibrary)'
            ]
        }
        
        results = []
        for string in strings:
            for category, patterns in suspicious_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, string):
                        results.append({
                            'string': string,
                            'category': category,
                            'pattern': pattern,
                            'confidence': 'high' if len(string) > 10 else 'medium'
                        })
                        break
        
        return results

    def parse_import_table(self) -> List[Dict]:
        """Parse the Import Address Table (IAT)"""
        imports = []
        try:
            # Get optional header
            opt_header_offset = self.pe_header_offset + 24
            magic = struct.unpack('<H', self.data[opt_header_offset:opt_header_offset+2])[0]

            if magic == 0x10b:  # PE32
                import_table_rva_offset = opt_header_offset + 96
            elif magic == 0x20b:  # PE32+
                import_table_rva_offset = opt_header_offset + 112
            else:
                print("[-] Unknown PE format")
                return imports

            # Get import table RVA and size
            import_rva = struct.unpack('<I', self.data[import_table_rva_offset:import_table_rva_offset+4])[0]
            import_size = struct.unpack('<I', self.data[import_table_rva_offset+4:import_table_rva_offset+8])[0]

            if import_rva == 0:
                print("[-] No import table found")
                return imports

            # Convert RVA to file offset
            import_offset = self.rva_to_offset(import_rva)
            if import_offset == 0:
                return imports

            # Parse import descriptors
            descriptor_size = 20
            offset = import_offset

            while offset < len(self.data) - descriptor_size:
                descriptor = self.data[offset:offset+descriptor_size]

                # Check if we've reached the end (null descriptor)
                if descriptor == b'\x00' * descriptor_size:
                    break

                # Parse descriptor fields
                import_lookup_rva = struct.unpack('<I', descriptor[0:4])[0]
                timestamp = struct.unpack('<I', descriptor[4:8])[0]
                forwarder_chain = struct.unpack('<I', descriptor[8:12])[0]
                name_rva = struct.unpack('<I', descriptor[12:16])[0]
                import_address_rva = struct.unpack('<I', descriptor[16:20])[0]

                if name_rva == 0:
                    break

                # Get DLL name
                name_offset = self.rva_to_offset(name_rva)
                if name_offset > 0:
                    dll_name = self.read_cstring(name_offset)

                    # Get imported functions
                    functions = self.parse_import_functions(import_lookup_rva, import_address_rva)

                    imports.append({
                        'dll': dll_name,
                        'functions': functions,
                        'suspicious': self.is_suspicious_dll(dll_name)
                    })

                offset += descriptor_size

        except Exception as e:
            print(f"[-] Error parsing import table: {e}")

        return imports

    def rva_to_offset(self, rva: int) -> int:
        """Convert RVA to file offset"""
        try:
            # Parse section headers
            coff_header_offset = self.pe_header_offset + 4
            num_sections = struct.unpack('<H', self.data[coff_header_offset+2:coff_header_offset+4])[0]
            opt_header_size = struct.unpack('<H', self.data[coff_header_offset+16:coff_header_offset+18])[0]

            section_header_offset = self.pe_header_offset + 24 + opt_header_size

            for i in range(num_sections):
                section_offset = section_header_offset + (i * 40)
                section_data = self.data[section_offset:section_offset+40]

                virtual_address = struct.unpack('<I', section_data[12:16])[0]
                virtual_size = struct.unpack('<I', section_data[8:12])[0]
                raw_data_offset = struct.unpack('<I', section_data[20:24])[0]

                if virtual_address <= rva < virtual_address + virtual_size:
                    return raw_data_offset + (rva - virtual_address)

        except Exception as e:
            print(f"[-] Error converting RVA to offset: {e}")

        return 0

    def read_cstring(self, offset: int) -> str:
        """Read null-terminated string from offset"""
        try:
            end = self.data.find(b'\x00', offset)
            if end == -1:
                end = len(self.data)
            return self.data[offset:end].decode('ascii', errors='ignore')
        except:
            return ""

    def parse_import_functions(self, lookup_rva: int, address_rva: int) -> List[str]:
        """Parse imported function names"""
        functions = []
        try:
            if lookup_rva == 0:
                lookup_rva = address_rva

            lookup_offset = self.rva_to_offset(lookup_rva)
            if lookup_offset == 0:
                return functions

            offset = lookup_offset
            while offset < len(self.data) - 8:
                # Read import lookup table entry
                entry = struct.unpack('<Q', self.data[offset:offset+8])[0]

                if entry == 0:
                    break

                # Check if import by ordinal
                if entry & 0x8000000000000000:
                    ordinal = entry & 0xFFFF
                    functions.append(f"Ordinal_{ordinal}")
                else:
                    # Import by name
                    hint_name_rva = entry & 0x7FFFFFFF
                    hint_name_offset = self.rva_to_offset(hint_name_rva)
                    if hint_name_offset > 0:
                        # Skip hint (2 bytes) and read name
                        func_name = self.read_cstring(hint_name_offset + 2)
                        if func_name:
                            functions.append(func_name)

                offset += 8

        except Exception as e:
            print(f"[-] Error parsing import functions: {e}")

        return functions

    def is_suspicious_dll(self, dll_name: str) -> bool:
        """Check if DLL name is suspicious"""
        suspicious_dlls = [
            'ntdll.dll', 'kernel32.dll', 'advapi32.dll', 'user32.dll',
            'wininet.dll', 'winhttp.dll', 'urlmon.dll', 'ws2_32.dll',
            'psapi.dll', 'dbghelp.dll', 'imagehlp.dll', 'version.dll'
        ]
        return dll_name.lower() in suspicious_dlls

    def analyze_suspicious_imports(self, imports: List[Dict]) -> List[Dict]:
        """Analyze imports for suspicious functions"""
        suspicious_functions = {
            'process_injection': [
                'VirtualAlloc', 'VirtualAllocEx', 'VirtualProtect', 'VirtualProtectEx',
                'WriteProcessMemory', 'ReadProcessMemory', 'CreateRemoteThread',
                'CreateRemoteThreadEx', 'NtCreateThreadEx', 'RtlCreateUserThread',
                'SetThreadContext', 'GetThreadContext', 'ResumeThread', 'SuspendThread',
                'QueueUserAPC', 'NtQueueApcThread', 'NtMapViewOfSection', 'ZwMapViewOfSection'
            ],
            'process_manipulation': [
                'OpenProcess', 'CreateProcess', 'CreateProcessA', 'CreateProcessW',
                'TerminateProcess', 'GetCurrentProcess', 'GetProcessId', 'EnumProcesses',
                'Process32First', 'Process32Next', 'CreateToolhelp32Snapshot'
            ],
            'evasion': [
                'IsDebuggerPresent', 'CheckRemoteDebuggerPresent', 'NtQueryInformationProcess',
                'ZwQueryInformationProcess', 'GetTickCount', 'Sleep', 'SleepEx',
                'SetUnhandledExceptionFilter', 'UnhandledExceptionFilter'
            ],
            'persistence': [
                'RegOpenKey', 'RegCreateKey', 'RegSetValue', 'RegQueryValue',
                'CreateService', 'OpenService', 'StartService', 'ControlService',
                'SetWindowsHookEx', 'CreateFile', 'WriteFile', 'CopyFile'
            ],
            'network': [
                'InternetOpen', 'InternetConnect', 'HttpOpenRequest', 'HttpSendRequest',
                'URLDownloadToFile', 'WinHttpOpen', 'WinHttpConnect', 'socket',
                'connect', 'send', 'recv', 'WSAStartup', 'WSASocket'
            ],
            'crypto': [
                'CryptAcquireContext', 'CryptCreateHash', 'CryptHashData',
                'CryptDeriveKey', 'CryptEncrypt', 'CryptDecrypt'
            ]
        }

        results = []
        for import_info in imports:
            dll_name = import_info['dll']
            for func_name in import_info['functions']:
                for category, func_list in suspicious_functions.items():
                    if func_name in func_list:
                        results.append({
                            'dll': dll_name,
                            'function': func_name,
                            'category': category,
                            'confidence': 'high'
                        })

        return results

    def run_analysis(self) -> Dict:
        """Run complete analysis"""
        print(f"[+] Analyzing: {self.filepath}")
        print("=" * 60)

        if not self.load_file():
            return {}

        if not self.parse_pe_header():
            return {}

        # Extract and analyze strings
        print("[+] Extracting strings...")
        all_strings = self.extract_strings(min_length=4)
        print(f"[+] Found {len(all_strings)} strings")

        print("[+] Analyzing suspicious strings...")
        suspicious_strings = self.analyze_suspicious_strings(all_strings)

        # Parse and analyze imports
        print("[+] Parsing import table...")
        imports = self.parse_import_table()
        print(f"[+] Found {len(imports)} imported DLLs")

        print("[+] Analyzing suspicious imports...")
        suspicious_imports = self.analyze_suspicious_imports(imports)

        return {
            'file_info': {
                'path': self.filepath,
                'size': len(self.data),
                'pe_header_offset': self.pe_header_offset
            },
            'strings': {
                'total': len(all_strings),
                'suspicious': suspicious_strings,
                'all_strings': all_strings[:100]  # Limit output
            },
            'imports': {
                'total_dlls': len(imports),
                'all_imports': imports,
                'suspicious': suspicious_imports
            }
        }

def print_results(results: Dict):
    """Print analysis results in a formatted way"""
    if not results:
        print("[-] No results to display")
        return

    print("\n" + "="*60)
    print("ANALYSIS RESULTS")
    print("="*60)

    # File info
    file_info = results.get('file_info', {})
    print(f"\n[FILE INFO]")
    print(f"Path: {file_info.get('path', 'Unknown')}")
    print(f"Size: {file_info.get('size', 0):,} bytes")
    print(f"PE Header Offset: 0x{file_info.get('pe_header_offset', 0):x}")

    # Suspicious strings
    strings_info = results.get('strings', {})
    suspicious_strings = strings_info.get('suspicious', [])
    print(f"\n[SUSPICIOUS STRINGS] ({len(suspicious_strings)} found)")

    categories = {}
    for item in suspicious_strings:
        category = item['category']
        if category not in categories:
            categories[category] = []
        categories[category].append(item)

    for category, items in categories.items():
        print(f"\n  {category.upper()}:")
        for item in items[:10]:  # Limit to 10 per category
            print(f"    - {item['string'][:80]}...")

    # Suspicious imports
    imports_info = results.get('imports', {})
    suspicious_imports = imports_info.get('suspicious', [])
    print(f"\n[SUSPICIOUS IMPORTS] ({len(suspicious_imports)} found)")

    import_categories = {}
    for item in suspicious_imports:
        category = item['category']
        if category not in import_categories:
            import_categories[category] = []
        import_categories[category].append(item)

    for category, items in import_categories.items():
        print(f"\n  {category.upper()}:")
        for item in items:
            print(f"    - {item['dll']} -> {item['function']}")

    # All imports summary
    all_imports = imports_info.get('all_imports', [])
    print(f"\n[ALL IMPORTS] ({len(all_imports)} DLLs)")
    for import_info in all_imports:
        dll_name = import_info['dll']
        func_count = len(import_info['functions'])
        suspicious_flag = " [SUSPICIOUS]" if import_info['suspicious'] else ""
        print(f"  - {dll_name} ({func_count} functions){suspicious_flag}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python analyze_exe.py <pe_file>")
        sys.exit(1)

    filepath = sys.argv[1]
    if not os.path.exists(filepath):
        print(f"[-] File not found: {filepath}")
        sys.exit(1)

    analyzer = PEAnalyzer(filepath)
    results = analyzer.run_analysis()
    print_results(results)
