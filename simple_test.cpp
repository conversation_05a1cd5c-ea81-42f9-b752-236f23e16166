#include <windows.h>
#include <stdio.h>
#include "skCrypter.h"

int main() {
    // Test 1: Plaintext strings (should be easily detectable)
    printf("PLAINTEXT_TEST_STRING_1\n");
    printf("PLAINTEXT_TEST_STRING_2\n");
    
    // Test 2: skCrypt strings (supposedly encrypted)
    auto encrypted1 = skCrypt("SKCRYPT_TEST_STRING_1");
    printf("Encrypted 1: %s\n", encrypted1.decrypt());
    encrypted1.clear();
    
    auto encrypted2 = skCrypt("SKCRYPT_TEST_STRING_2");
    printf("Encrypted 2: %s\n", encrypted2.decrypt());
    encrypted2.clear();
    
    // Test 3: Common debug patterns
    auto debug1 = skCrypt("[DEBUG] This should be encrypted");
    printf("%s\n", debug1.decrypt());
    debug1.clear();
    
    auto info1 = skCrypt("[INFO] This should also be encrypted");
    printf("%s\n", info1.decrypt());
    info1.clear();
    
    auto error1 = skCrypt("[-] ERROR: This should be encrypted too");
    printf("%s\n", error1.decrypt());
    error1.clear();
    
    return 0;
}
