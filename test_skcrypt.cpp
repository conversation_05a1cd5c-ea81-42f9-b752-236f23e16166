#include <iostream>
#include <cstdio>
#include <cstring>
#include "skCrypter.h"

// Test safe_print function with skCrypt
template<typename T>
int safe_print(T msg)
{
    auto eFormat = skCrypt("%s\n");
    printf(eFormat.decrypt(), msg.decrypt());
    eFormat.clear();
    msg.clear();
    return 0;
}

template<typename T>
int safe_print(T msg, int value)
{
    auto eFormat = skCrypt("%s%d\n");
    printf(eFormat.decrypt(), msg.decrypt(), value);
    eFormat.clear();
    msg.clear();
    return 0;
}

// Test plaintext vs encrypted
void test_plaintext() {
    printf("PLAINTEXT_DEBUG_MESSAGE\n");
    printf("PLAINTEXT_INFO_MESSAGE\n");
    printf("PLAINTEXT_ERROR_MESSAGE\n");
}

void test_skcrypt() {
    // Test direct skCrypt usage
    auto encrypted_msg = skCrypt("ENCRYPTED_DEBUG_MESSAGE");
    printf("Encrypted: %s\n", encrypted_msg.decrypt());
    encrypted_msg.clear();

    // Test safe_print with skCrypt
    safe_print(skCrypt("ENCRYPTED_INFO_MESSAGE"));
    safe_print(skCrypt("ENCRYPTED_ERROR_MESSAGE_WITH_VALUE"), 42);

    // Test common malware strings
    safe_print(skCrypt("[DEBUG] Trying to resolve module"));
    safe_print(skCrypt("[DEBUG] Module hash"));
    safe_print(skCrypt("[DEBUG] Found module via MoonWalkToModule"));
    safe_print(skCrypt("[INFO] Hijacked I/O completion handle"));
    safe_print(skCrypt("[-] ERROR: Function not found in remote DLL"));
    safe_print(skCrypt("NtAllocateVirtualMemory"));
    safe_print(skCrypt("ZwSetIoCompletion"));
    safe_print(skCrypt("TP_DIRECT"));
    safe_print(skCrypt("xpsservices.dll"));
    safe_print(skCrypt("mstsc.exe"));
}

int main()
{
    std::cout << "=== skCrypt Encryption Test ===" << std::endl;

    std::cout << "\n--- Plaintext Strings (should be easily detectable) ---" << std::endl;
    test_plaintext();

    std::cout << "\n--- skCrypt Encrypted Strings (should be harder to detect) ---" << std::endl;
    test_skcrypt();

    std::cout << "\nTest completed. Now analyze with strings utility to see if skCrypt works." << std::endl;
    return 0;
}
