Analyzing: asd_secured.exe
File size: 408,064 bytes
Platform: Windows

Detecting system analysis tools...
Available tools:
  - objdump: C:\msys64\mingw64\bin\objdump.exe
  - strings: C:\msys64\mingw64\bin\strings.exe

Extracting strings...
  - Using system strings utility

Extracting imports...
  - System tool import analysis available
    * objdump output captured
Analyzing malware indicators...
Searching for obfuscated APIs...
Extracting target information...
================================================================================
ROBUST MALWARE ANALYSIS RESULTS
================================================================================

TOTAL STRINGS EXTRACTED: 8303

MALWARE INDICATORS:
----------------------------------------

PROCESS INJECTION (9 matches):
  ò [INFO]  Crafted TP_DIRECT structure associated with the shellcode
  ò [INFO]  Allocated TP_DIRECT memory in the target process: %p
  ò aHaatpZBgKIFPcELxwF res (shellcode): 0x%x
  ò agdrYGgNoaubakbEoBq res (shellcode): 0x%x
  ò   VirtualProtect failed with code 0x%x
  ò ResumeThread
  ò SetThreadContext
  ò SuspendThread
  ò VirtualProtect

POOLPARTY TECHNIQUE (14 matches):
  ò [INFO]  Hijacked I/O completion handle from the target process: %x
  ò [INFO]  Crafted TP_DIRECT structure associated with the shellcode
  ò [INFO]  Allocated TP_DIRECT memory in the target process: %p
  ò [-] ERROR: Failed to write TP_DIRECT structure: 0x%x
  ò [INFO]  Written the TP_DIRECT structure to the target process
  ò [DEBUG] I/O Completion Handle: 0x%p
  ò [DEBUG] TP_DIRECT Address: 0x%p
  ò [DEBUG] About to call Sw3NtSetIoCompletion
  ò [DEBUG] Using pure PEB walking for ZwSetIoCompletion
  ò [-] ERROR: Failed to resolve ZwSetIoCompletion via manual export parsi...
  ... and 4 more

MODULE STOMPING (4 matches):
  ò [DEBUG] xpsservices.dll not found, trying kernel32.dll
  ò xpsservices.dll
  ò DllCanUnloadNow
  ò LoadLibraryW

EVASION TECHNIQUES (19 matches):
  ò [DEBUG] Trying to resolve module: %s
  ò [DEBUG] Module hash: 0x%x
  ò [DEBUG] Found module via MoonWalkToModule
  ò [DEBUG] Found module via GetModuleByHash
  ò [DEBUG] xpsservices.dll not found, trying kernel32.dll
  ò [DEBUG] Trying manual mapping
  ò [DEBUG] Found module via manual mapping
  ò [DEBUG] Manual mapping failed
  ò [DEBUG] Parsing remote module at: 0x%p for function: %s
  ò [DEBUG] Found function at remote address: 0x%p
  ... and 9 more

SYSCALLS DIRECT (41 matches):
  ò     =ntdlu
  ò <_t`<ntT
  ò x	NtAf
  ò 	yZwa5
  ò B>NtQ
  ò ZwBT
  ò zwl*
  ò a@TBfnTc,E2wf
  ò NtQueryObject
  ò ntdll.dll
  ... and 31 more

TARGET PROCESSES (2 matches):
  ò mstsc.exe
  ò OpenProcess

CRYPTO OBFUSCATION (3 matches):
  ò global constructors keyed to 
  ò global destructors keyed to 
  ò Error cleaning up spin_keys for thread %lu.

DEBUG ARTIFACTS (29 matches):
  ò [DEBUG] Trying to resolve module: %s
  ò [DEBUG] Module hash: 0x%x
  ò [DEBUG] Found module via MoonWalkToModule
  ò [DEBUG] Found module via GetModuleByHash
  ò [DEBUG] xpsservices.dll not found, trying kernel32.dll
  ò [DEBUG] Trying manual mapping
  ò [DEBUG] Found module via manual mapping
  ò [DEBUG] Manual mapping failed
  ò [DEBUG] Parsing remote module at: 0x%p for function: %s
  ò [DEBUG] Found function at remote address: 0x%p
  ... and 19 more

TOTAL SUSPICIOUS INDICATORS: 121

OBFUSCATED API NAMES (4 found):
----------------------------------------
  ò SystemFunction036
  ò IsDebuggerPresent
  ò IsProcessorFeaturePresent
  ò RtlLookupFunctionEntry

TARGET INFORMATION:
----------------------------------------

PROCESSES (2 unique):
  ò main_exe_%p.exe
  ò mstsc.exe

DLLS (9 unique):
  ò kernelbase.dll
  ò ntdll.dll
  ò dll_%p.dll
  ò [DEBUG] xpsservices.dll not found, trying kernel32.dll
  ò kernel32.dll
  ò msvcrt.dll
  ò KERNEL32.dll
  ò xpsservices.dll
  ò module_%p.dll

FUNCTIONS (405 unique):
  ò JTBf
  ò RAPAQ
  ò QPbd
  ò NfQC4
  ò GetProcAddress
  ò MeiBv
  ò Autht
  ò Vsay
  ò LYFF
  ò LocalFree
  ... and 395 more

FILE PATHS (2 unique):
  ò C:\WindoH
  ò \\<[,

================================================================================
SYSTEM TOOL ANALYSIS RESULTS
================================================================================

OBJDUMP OUTPUT:
----------------------------------------
  asd_secured.exe:     file format pei-x86-64
  Characteristics 0x22e
  	executable
  	line numbers stripped
  	symbols stripped
  	large address aware
  	debugging information removed
  Time/Date		Sun Aug 10 18:21:54 2025
  Magic			020b	(PE32+)
  MajorLinkerVersion	2
  MinorLinkerVersion	44
  SizeOfCode		0000000000038800
  SizeOfInitializedData	000000000002ae00
  SizeOfUninitializedData	0000000000001c00
  AddressOfEntryPoint	0000000000001400
  BaseOfCode		0000000000001000
  ImageBase		0000000140000000
  SectionAlignment	00001000
  FileAlignment		00000200
  MajorOSystemVersion	4
  MinorOSystemVersion	0
  MajorImageVersion	0
  MinorImageVersion	0
  MajorSubsystemVersion	5
  MinorSubsystemVersion	2
  Win32Version		00000000
  SizeOfImage		0006b000
  SizeOfHeaders		00000400
  CheckSum		00067234
  Subsystem		00000003	(Windows CUI)
  DllCharacteristics	00000160
  					HIGH_ENTROPY_VA
  					DYNAMIC_BASE
  					NX_COMPAT
  SizeOfStackReserve	0000000000200000
  SizeOfStackCommit	0000000000001000
  SizeOfHeapReserve	0000000000100000
  SizeOfHeapCommit	0000000000001000
  LoaderFlags		00000000
  NumberOfRvaAndSizes	00000010
  The Data Directory
  Entry 0 0000000000000000 00000000 Export Directory [.edata (or where ever we found it)]
  Entry 1 0000000000066000 00001168 Import Directory [parts of .idata]
  Entry 2 0000000000069000 000004e8 Resource Directory [.rsrc]
  Entry 3 000000000005c000 000039c0 Exception Directory [.pdata]
  Entry 4 0000000000000000 00000000 Security Directory
  ... (9161 more lines)
